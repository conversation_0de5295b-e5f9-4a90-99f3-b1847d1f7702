{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bdca1e54b6896bd23aff0fb1684db3c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98789c94038e1d85916fd3c87e93070fdf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ace443f7ef67b0d50931207c24ec64ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f05bcb80abf8d2ff78437d5ec7d8ff06", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ace443f7ef67b0d50931207c24ec64ae", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9841e8ae84b26a2be1f0eff502daeac66d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a939b454b18b6b01cedd81a68ef9f6d3", "guid": "bfdfe7dc352907fc980b868725387e981633732e5a09d366b50231106b02bbb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5e798c4430f47ecbb2bdcde70f74362", "guid": "bfdfe7dc352907fc980b868725387e985d1e77a9e0f21d15d95d15d10c36a7ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981255b7ffac91718c36a766cc0a6edd90", "guid": "bfdfe7dc352907fc980b868725387e98f9fb8811798c570fc172e2797b9932ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8c897f5ff6a6a61d2365a4e39ba5f83", "guid": "bfdfe7dc352907fc980b868725387e9808e1e798861d3ee21cd6773413d75586", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98896405e7a7b4f95e84bfb49a9fef7f88", "guid": "bfdfe7dc352907fc980b868725387e98bb70f6e003c88bd57d121f7428595810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853a8e19b3aef57dda94c140c956ba737", "guid": "bfdfe7dc352907fc980b868725387e98ee60b9264fa08624ca1ba3f5e2f6b8db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449e9ba862bd5d26eb1f4680cbb5e189", "guid": "bfdfe7dc352907fc980b868725387e988b7dba85ab45c83a54ab6625a1410cd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f4e5038b5e98dacf6dee383bfb7f6f", "guid": "bfdfe7dc352907fc980b868725387e98ea0dd9538ba446c379f36f4e9d5f2082", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e988f67ebbfe9a44a955f668d3a5eb4c0bb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aaca6170bc464a1ab5ddfc69f4f0b5fe", "guid": "bfdfe7dc352907fc980b868725387e98ecb41fbfb930c36ae115d09d3f3d730e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6e28966dc6e3970877d8a26fa9d225", "guid": "bfdfe7dc352907fc980b868725387e984c208bdbaf82b8ded757b24680f0d554"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e95a2a050a03f51677296eef3f1351", "guid": "bfdfe7dc352907fc980b868725387e9835f8e188942a1abdf7bcb990056084eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980df15d69614f9bbfff3573a124ff5bbe", "guid": "bfdfe7dc352907fc980b868725387e98e98f31d8514624f14c4fe471834cb1b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa0f093a6ec561e6b7cf9e40a6491e0b", "guid": "bfdfe7dc352907fc980b868725387e98c8aeceffdcea7357fd42b6124001a569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9aea742a612535d4921f1f8f632a57b", "guid": "bfdfe7dc352907fc980b868725387e98fefa17d8cca219df8c3d0a3f6af0b05a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab469d4dde1aa60020c57f9f817c1ef7", "guid": "bfdfe7dc352907fc980b868725387e983b67e8aba06b88ad1574eb63f32fcf7c"}], "guid": "bfdfe7dc352907fc980b868725387e98d0a3957f9e1e76017912b92f0fab46b9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98daa3520e3d9f06a110e8f01981af6f59"}], "guid": "bfdfe7dc352907fc980b868725387e98ce97f70f77fd0c53336335476b6c74ce", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98021c1a71a666361c883fecc3ff1b4e14", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e983788d8769c821650606514be955fca93", "name": "firebase_auth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984496a3f7661d89567ff8250961054e8f", "name": "firebase_auth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}