{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98243426616c1f35aa8fab7584e1189ca5", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810ea0f765cbacff463ca2bb5a32d5053", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c9b1de34c0a82b94482dda6b3f0a8b0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988445daaecbeefd5490ce2909074c2093", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c9b1de34c0a82b94482dda6b3f0a8b0", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5f954ad9bf5829d13049b5e5f211a6d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd763e9a228ba574c344cc1054d76ac9", "guid": "bfdfe7dc352907fc980b868725387e98b0a3f33bbdde5aa48ee9a730eec9aed6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2970a0500adf535c853ce4655093917", "guid": "bfdfe7dc352907fc980b868725387e98f3c45feb601d0d94259d9fc69d6db471", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7edf8e4b196e6849d2a1d4c8abf0696", "guid": "bfdfe7dc352907fc980b868725387e98b41b610cf08dd9dafcbb6722048300a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810f40f5d9ad4c75783f487fee5a8b90d", "guid": "bfdfe7dc352907fc980b868725387e98ad970b7219a9f5866a3db21110281887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820fa08910f12ad34597818a5dfbb62b4", "guid": "bfdfe7dc352907fc980b868725387e9809bdf61170603aefff51bce4b1f24c9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854b682ccae54684b4331a23393af15dd", "guid": "bfdfe7dc352907fc980b868725387e981e401233c7badc4a0e77e9554283b185", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea9d409cf2bb2f6387871d23ab4b71a7", "guid": "bfdfe7dc352907fc980b868725387e9891d1b1117356a1d05205849f5a7209c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ef78148cb89054a06ec953881908724", "guid": "bfdfe7dc352907fc980b868725387e98103525d3c88ddfc7b71f571f07402da8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0950140d00893a445640fd558763339", "guid": "bfdfe7dc352907fc980b868725387e98abf6abc4ab889254f1644a6a7f201d1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828cc4add53a72a97639ed44bdc3f3f6e", "guid": "bfdfe7dc352907fc980b868725387e989bcec3ce91ea61164003caa6b724fb8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b983a458f0bc77ba286cd9392e040bd", "guid": "bfdfe7dc352907fc980b868725387e980c3692478a20a971f3e83022c6a1badf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b272026c51e273f57792ea1c588516", "guid": "bfdfe7dc352907fc980b868725387e98112902c72ef392ce5b4060c57bc35b83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983111ff16dd5e4a30e33933621a02220c", "guid": "bfdfe7dc352907fc980b868725387e98050d2a2ff68ea96388b7476db9bf56b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cf37ed7370ee966e4ac228e64ae895c", "guid": "bfdfe7dc352907fc980b868725387e98f0a9ae706a6edfbfe962f6f56bbca1e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e29c94f5c5de19a331ccc36f35921ff7", "guid": "bfdfe7dc352907fc980b868725387e980b8c041e263506840fd9a22a1144ee84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837fa0d56b3862c5d1a09a436ceef685a", "guid": "bfdfe7dc352907fc980b868725387e989050274f110fd07483815086d416d671", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984515ebab06224bddd6a02e6f732718dc", "guid": "bfdfe7dc352907fc980b868725387e9894ad594067b67b4b9b9ae2b84125693b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa79ef41c06dc459abb8219cbc5cdd82", "guid": "bfdfe7dc352907fc980b868725387e9800db2c1a0080fa7e91f7902a6d10e39f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98049633dc550e8d352d8b735206d500fe", "guid": "bfdfe7dc352907fc980b868725387e984d985ca68335b71d14213924333e4625", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2141b077e14f23a0d2dab9955112f2c", "guid": "bfdfe7dc352907fc980b868725387e98a66e7d380f5472c240ec63588806ab4a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08b0fc9efeeac6fc5bb99424bd802e3", "guid": "bfdfe7dc352907fc980b868725387e982b7ade812c219758deb4a19ce7d9d3f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98795585c7645069fe495c9a5474fcb692", "guid": "bfdfe7dc352907fc980b868725387e986c9cbe82f9ea3512081b615d47a40629", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a272926f37349df6c9d30d189b3eff6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c7c14e375b9925ab0f0a612c9a9afcf", "guid": "bfdfe7dc352907fc980b868725387e98fa42deec8dd8c32503248af38af78ded"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885364676b828e15d9f9c8152a88b6b7a", "guid": "bfdfe7dc352907fc980b868725387e9844a2e2f6012fd7af023f62ad269f3b7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6663c427f7e7104b30d6df7fa3e6d91", "guid": "bfdfe7dc352907fc980b868725387e987be6fd14ea72d789e4c02d1fd1e35ba9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809d5b24a489682df65c172a21eeda6a3", "guid": "bfdfe7dc352907fc980b868725387e98687e0435c3270d28a3d5bb360288e191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b4ac14772108a5f417234008e5de723", "guid": "bfdfe7dc352907fc980b868725387e98e3a9c927b198d12a4fb7d9833a1f7592"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd70900f7f6adc1740d7cecdbb94cc89", "guid": "bfdfe7dc352907fc980b868725387e987c3dcc6f62caf4effef02430f6ee51e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a2374ad2eb2be1fcbd83a6b7272423d", "guid": "bfdfe7dc352907fc980b868725387e98c7a6bc8dc0e2cc7fb4c2e27fbd577668"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98516867671bb4ce5579f9aa1f89eb667e", "guid": "bfdfe7dc352907fc980b868725387e98efa79198e5c83bdd640907c8bcc08f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98761e0f34842d85f1fcd9dc29fdfed582", "guid": "bfdfe7dc352907fc980b868725387e985aee9c00ef1cb5c593e623440fae4227"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b2132c151656bd60c9091725a791b2", "guid": "bfdfe7dc352907fc980b868725387e9801d98afd4461cf0bac7cfe3c92f32887"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e3c6d0d751e406bbe198adc5ad77fd8", "guid": "bfdfe7dc352907fc980b868725387e98f6a264a4c8140ed78eefd1e37a8ecc1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98154e72f3b35f2f3cb9f828e5f89b23e5", "guid": "bfdfe7dc352907fc980b868725387e9885cff54e6078d86dbd0303285d094153"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0b14d4f79918648e195d424a67ccb11", "guid": "bfdfe7dc352907fc980b868725387e98fe6a51e5239159291a6de2470c02bc90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e620fcc400aec3ed49f3c68b15256efe", "guid": "bfdfe7dc352907fc980b868725387e98d4d7d10eb7105af56925130d77d8ab43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98120350d1f790c188ed93c8c2e0937704", "guid": "bfdfe7dc352907fc980b868725387e981e72e58ecf635301cf1647772b756069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e39ed4201c5fc36a8d26cc9f443413a5", "guid": "bfdfe7dc352907fc980b868725387e982534991a24aee5df5ee059aad32664b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e53b1284838abcb643e70acbad6fe62", "guid": "bfdfe7dc352907fc980b868725387e989b2aa2401dbc2548f87fd40b854b0280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983032a66e1148e963678de19727cf059c", "guid": "bfdfe7dc352907fc980b868725387e985b8c1c71e16d04a6442df0aeb6c14801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc547afd0f52ad54d5aec07e0341bb2", "guid": "bfdfe7dc352907fc980b868725387e98f3935ee065d61c7afc1652e7dbcc23a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98734ae82238e371103978b91909339803", "guid": "bfdfe7dc352907fc980b868725387e98cfc1e32d8201b07471c648bcf30e3a3f"}], "guid": "bfdfe7dc352907fc980b868725387e984057e5edf2cedbd65ad8a57c76a88736", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98e82eb87c3e6eeabc4c9afaf014a37e1f"}], "guid": "bfdfe7dc352907fc980b868725387e98d8d0c40378148302fb8d01a67f850216", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e5035414c20dd8737ac9aa706986f90d", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e983df84c9936c8e228d176b2f34485a57c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}