{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98668d2db6b14169df2cabba6490bcb89e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885717fdd163ab470b484d7d0bd56f9da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885717fdd163ab470b484d7d0bd56f9da", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b77b9fad50f270e9eebd016d9fbdf973", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f44f16858f62bfa8ff6524a96b68f608", "guid": "bfdfe7dc352907fc980b868725387e98dd43c746d9b9bbca2caf3f40469dc2ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566f6fa36a24416ca5afaf640d4b3e2f", "guid": "bfdfe7dc352907fc980b868725387e9837316f0ac0a6699e2f423b2caf33bb5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896cffddc017c4a2d755b153fd2534dc9", "guid": "bfdfe7dc352907fc980b868725387e9865ec75afcca57b830aa4abda3a06d060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7ba6c34b593836fd6040a8bfa7b6bcd", "guid": "bfdfe7dc352907fc980b868725387e98a7b780ed8cf2de6962a4df2f0856dac1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c300ae774cd42d65632375100a37a825", "guid": "bfdfe7dc352907fc980b868725387e98fb016c9103489d29845c6718f6462b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ecb9995a583f81b8d03e805f184e866", "guid": "bfdfe7dc352907fc980b868725387e98589491648475e39189eac44bfd7f7f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987362c7cfcf6dc8ffc55f582988b946e2", "guid": "bfdfe7dc352907fc980b868725387e98182f130f4e9cadceba05a62bd6cd840a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854dffb671c54c96c834095ae6660dc5b", "guid": "bfdfe7dc352907fc980b868725387e98e8e125fa7e6615a07f79e1e8a7f33ed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98536b86f165878acee9ae349aafcbaf4a", "guid": "bfdfe7dc352907fc980b868725387e987ad4ad2d0de7cfbb6b667119acbb16bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6e67e9a767166ebf2a2ab7d643551a", "guid": "bfdfe7dc352907fc980b868725387e980c89930099eb4b9ab2a9e268d52ba00e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984740c436f44ce4feae25445a3d3b0203", "guid": "bfdfe7dc352907fc980b868725387e9831f91e0ed32c5eb5c5c07a536c780068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbbebbdc339818b41af084f09c01d382", "guid": "bfdfe7dc352907fc980b868725387e986a85e81b1d5203fd7ed8d1e1bb4fd415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e655f37d92e80b135161fe916588e30", "guid": "bfdfe7dc352907fc980b868725387e98dc4da732b2e9f2f6e3b9f5a230f809a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6346d1efec7915c7ffecfecd76f029", "guid": "bfdfe7dc352907fc980b868725387e98bbee91f3942d1437707e4f9cea35b71a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98280f2c5944be11864fa3ab9d9190f2e3", "guid": "bfdfe7dc352907fc980b868725387e98d1443b9b544f368d8cfd5d2a6f39f45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840d666f2a24e2b0844137cd155c08189", "guid": "bfdfe7dc352907fc980b868725387e9803ee64163e1f39027c9c4348127b3de0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea804495b3b3e1436ec50ffd63416ea", "guid": "bfdfe7dc352907fc980b868725387e98bc9e8ca2830a296b752098b6797011df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5467158b9a1f2eab79ef228fd436cc2", "guid": "bfdfe7dc352907fc980b868725387e98a27e05eda3b9f43b6a6a14c98ce174f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a75c078befbd94e5917fbaf6d326341a", "guid": "bfdfe7dc352907fc980b868725387e98c518f88905401e3f640ffcaf28f84251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98506a0a3180e258a616a3fbeed688574c", "guid": "bfdfe7dc352907fc980b868725387e9815b7584173468f9b8cc0dd60db934d0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8edebb088d6cba021b0b4bad41a0c2", "guid": "bfdfe7dc352907fc980b868725387e981918332a5fb1c07efaba9a77ad4d4e90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98213fa767b3945abec2f220cd985aec86", "guid": "bfdfe7dc352907fc980b868725387e98e9aa4ebfca13f867632ba43e5cec156d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886ac718ae9d48590f06d3f27a89bed3e", "guid": "bfdfe7dc352907fc980b868725387e98494da3168fcf181c59b6d704525cf3f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8e441c277e93bc44f1433c88db97c5f", "guid": "bfdfe7dc352907fc980b868725387e98d6ca6b8e3d26af31ba40e37e03d7e402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf5c6c1f6636404bbc2b460f1d86212c", "guid": "bfdfe7dc352907fc980b868725387e98b3c24932306aa4272554520f50e928c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f41b6f7b45f515a216b8b3f720c7b5b", "guid": "bfdfe7dc352907fc980b868725387e981e8b4ced0dccabf5f2d6086b3b71f1bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b0d88ebee4376cd87aebabf3e6c9733", "guid": "bfdfe7dc352907fc980b868725387e980cec672f728db91fca6b0b2a267bf388"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493c45811a3f4f23804b53d432f7f973", "guid": "bfdfe7dc352907fc980b868725387e98cbe74a6571a9181f7400344736d4792c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ace4d8b8885b89a894d10b739ed242", "guid": "bfdfe7dc352907fc980b868725387e98a367f4eceaa85bff32a3d7296a8d4617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4c6a4829c505e8747080027d2d5731d", "guid": "bfdfe7dc352907fc980b868725387e98e89f477e1f3368105dc9b888cc265bbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874dd4c8b3fc72f00d9a50f6b4971270", "guid": "bfdfe7dc352907fc980b868725387e98291c3975daa0308d1a4f66e1706c2233"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147716b493587520a43a9804dec44fca", "guid": "bfdfe7dc352907fc980b868725387e98e3bc6208e70fc8dbb50f101c1c4024e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877253db861f8f2e3ba8f3acb43069ce2", "guid": "bfdfe7dc352907fc980b868725387e98ab4b218e47c817e6e60d033045faf30f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658e54db87ba9f98164cc50c5535b6e7", "guid": "bfdfe7dc352907fc980b868725387e986f084ec05236a5aadb4e88f41e0de04d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98048afb712eabacfb6533c58cb5270f63", "guid": "bfdfe7dc352907fc980b868725387e9837cb0a48e7c8a9da14d5cced02111f35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6033d5d1a196da3ae65709b98610f43", "guid": "bfdfe7dc352907fc980b868725387e985c1cbf2f6c4ecda3d8584bbb4d22b18b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6cc30e48aa03e9425a2f529dce31a8", "guid": "bfdfe7dc352907fc980b868725387e988994dba980f5047cb1976b45d14baa7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8028f5d51ba5b196fdec8bd02b7b4c", "guid": "bfdfe7dc352907fc980b868725387e98d4bac29ad65ef4b3e00ac155bb8c8620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b54aa2827b1dc456eb79607f2ff64e5", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d121100c78386d78c6bf59230e6abe99", "guid": "bfdfe7dc352907fc980b868725387e986436d69972c6a1dd2cfefe3243441c54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fbabac6737d9a9dfce417a6645c1d81", "guid": "bfdfe7dc352907fc980b868725387e9800da3717a9214a3abeffac1aa47db983"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daf42b158773f202b187259fee8fe28a", "guid": "bfdfe7dc352907fc980b868725387e98ec2e26717e0504f81a02b143ffc6fb1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0a173cf3293167f606ed12c0c0ec74e", "guid": "bfdfe7dc352907fc980b868725387e9804c15db716d0612da50c11e272f444d4"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}