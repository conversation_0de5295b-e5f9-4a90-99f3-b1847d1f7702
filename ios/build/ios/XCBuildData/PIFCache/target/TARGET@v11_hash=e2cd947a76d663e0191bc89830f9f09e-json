{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980823616500c997f885001555cfbf5ec0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bc3a0e5dad85be4e0f88cd25d27b78ad", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b3e1c3c252815d41c3cc624c8cb355a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9846c63d2e82ff5473536e475af4139f25", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b3e1c3c252815d41c3cc624c8cb355a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988e766bcaa4cde99c1256df3cf21660db", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980db50f2e4e7c44b1260a497043a33c09", "guid": "bfdfe7dc352907fc980b868725387e9823d672856e6bb40fa76493c1cf9b6c3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c73dcf6745243efd21b2a01f2df3842d", "guid": "bfdfe7dc352907fc980b868725387e9833ab81b9bb48fef0364a1658d7f359b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876b5a6eb889b3f77f882eb2824e15158", "guid": "bfdfe7dc352907fc980b868725387e984b2438ca8b31568cbba591ed3cfe3527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493ab74fc9708601773ac1dab21fd0ce", "guid": "bfdfe7dc352907fc980b868725387e98a8043043817565dcdfd8cf32e82238f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981580c12bed4588f98d8825fde5dd7336", "guid": "bfdfe7dc352907fc980b868725387e98baeda11de942b869e8fee1b516ab3946"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98671e7faaa6d03b65608827d79afdd184", "guid": "bfdfe7dc352907fc980b868725387e98aee850a933b7250d1909f7e429ae9318"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982601da40f833f3004d97d61a769a10af", "guid": "bfdfe7dc352907fc980b868725387e982af6301eceb149eb564edc11b9f3a2da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989859fa89b57d8f2199dbea11bfb5efd6", "guid": "bfdfe7dc352907fc980b868725387e98bf8f95c5e2f68b9981bffcfd3fd30da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8e955954e0eb8656f32ad900613307", "guid": "bfdfe7dc352907fc980b868725387e9831ffa95fb1e8fcd776dfe333fa80f9d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee2ce6e71b01110b447c356ab4780bac", "guid": "bfdfe7dc352907fc980b868725387e989b6db119e96a17c54d405a41cde49dbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c74e3a15a6e903c42b52ce2ec999da86", "guid": "bfdfe7dc352907fc980b868725387e98c45c2431ecd889941efe9149f70c906d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eccc918e347d391d8d072b7ccf48fed2", "guid": "bfdfe7dc352907fc980b868725387e9868149242ed54d9ca416efc8733cf1178", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a88a72f941a86107f59e4b72947c797", "guid": "bfdfe7dc352907fc980b868725387e98117a55ad145319883427af02d71815c6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98340c72dc5a8fa1f8684259a1e937382f", "guid": "bfdfe7dc352907fc980b868725387e980347c6db6f80e5c772bf17853881759a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98610f8002b9302042628d9cfef8c56429", "guid": "bfdfe7dc352907fc980b868725387e9881d606da0595190795a4f953272b243f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaa4f5607176c56c8f538f2fd6f9982a", "guid": "bfdfe7dc352907fc980b868725387e986519504f7a186ebbd0c3e243e180f8ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a0c1dba0ba9129a3b306b1d4559413", "guid": "bfdfe7dc352907fc980b868725387e9870719b1546348e8aed72e3464583a627"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f329ec8ffd1108d2dfe9b8810e95fa5f", "guid": "bfdfe7dc352907fc980b868725387e98054883d9cfffcb832deb0b146f4c95db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3371e928f45f705f8c7aded24f4c65f", "guid": "bfdfe7dc352907fc980b868725387e988fe155a3ab7b87f777eb0db109342be4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987806b05177afee30a79f76af6c8f7d16", "guid": "bfdfe7dc352907fc980b868725387e9828ac5cd133148aac001b4d77663f25ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98100e032ae7e620da5fcbac5393412c63", "guid": "bfdfe7dc352907fc980b868725387e98f3f3f92c6d3811d6c9130f3e0507ce19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba269a639a5f7de3c71c144045781056", "guid": "bfdfe7dc352907fc980b868725387e98e10ed928d40f227460df3ba66c9e371c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9865f5ab434e5c3f62180e32e179200e93", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985bbe48212c8e7c90a38e3a43fa897a4c", "guid": "bfdfe7dc352907fc980b868725387e98dd8f0ee988b559eb8ccbd112b8f16a96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98437f3da241d9d6b0abce27501da6fea1", "guid": "bfdfe7dc352907fc980b868725387e98b0a242ec6cd356829205b8ce58f9602c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986549e254d9fc262bddd57871bea13ec8", "guid": "bfdfe7dc352907fc980b868725387e98ccd98281e8a5f56294fe6c388dac8a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988055caf4f12c3dca48751b49e96e8bfe", "guid": "bfdfe7dc352907fc980b868725387e9839533d119937b543f6f9b021b12af742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899bdc63e0cec2412a185305fc2c56a6b", "guid": "bfdfe7dc352907fc980b868725387e98bf4b3903b7e41ddf095fb327377af116"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb3debb8fcf0ea92f49c0dbc19c4f91", "guid": "bfdfe7dc352907fc980b868725387e98b5e492e8605dab77949336ab439fcc79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f1be8828fc15b491d680810b7d5f7f3", "guid": "bfdfe7dc352907fc980b868725387e98ab037c7ee68fd09b6f9e0309bc2acc5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d04d82c15469361ca2ec9e82d8d62785", "guid": "bfdfe7dc352907fc980b868725387e9813b0137bd29d7d82f107fcec3dc3eb3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2d77b7514f30fba873da4274b51b80b", "guid": "bfdfe7dc352907fc980b868725387e98211869c31278d92aa36d6b7a396b13fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4df8265864f38c51351ae9569a82de8", "guid": "bfdfe7dc352907fc980b868725387e987b9f2b895af38b6a31c53a2f6d672df9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98402aa8e37849712f7f442260b3eae9c3", "guid": "bfdfe7dc352907fc980b868725387e985ed8b29981e0216ac41378cdd3eb5a5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e55829223e3fc6b8c806850ae5c9239", "guid": "bfdfe7dc352907fc980b868725387e9872c913577570a7105a138f27489a6e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bd7a932d6138ae39c97383e18e4973e", "guid": "bfdfe7dc352907fc980b868725387e98cee3bf0356aa44b1229ff7dd142149fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982286e6a9ec706981a60fc3a8ee18fe13", "guid": "bfdfe7dc352907fc980b868725387e986323a94b980f8e3ac453dcd81a0d65a5"}], "guid": "bfdfe7dc352907fc980b868725387e98846ab25baab60c35bc4dca058cb6fba4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9845e9a9d888f52889e1da62bbd9e8892b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e51514501e0f0d62f7ef6096d26bdc0", "guid": "bfdfe7dc352907fc980b868725387e98951b0675a5bc03aeb76bff2665f7e34a"}], "guid": "bfdfe7dc352907fc980b868725387e981e789d25afda6386ee65e0f85eb475f7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986355013b46d2f46e7402ee3c366cb6a2", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9849516a1acad74ac7ae86adb03080eff4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}