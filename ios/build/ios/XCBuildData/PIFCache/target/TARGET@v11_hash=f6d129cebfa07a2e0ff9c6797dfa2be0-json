{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825c97d381aed8fd5c34d5fd2db46b125", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980cd268420c90b60198953a24659c35ed", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2c7128822611316a0c53554368706b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9850fb4efbac9df2e5c90489a783029494", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2c7128822611316a0c53554368706b7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834efdef0f2906ca6b101a2eaf9635299", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd2dd7bac708f7164e6704f878d0f896", "guid": "bfdfe7dc352907fc980b868725387e98beeefaffcdcf564bbe46b62f197b6b7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1fe59b1d8a1ac1bce53dd87110dc0ca", "guid": "bfdfe7dc352907fc980b868725387e9873c06255bf9843d8c01ce6d704c90c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fbf73cda5c9b1a49668425c6c85e10", "guid": "bfdfe7dc352907fc980b868725387e984d08b014ffcb646ae6df988cf1763943"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b9bce08589388bb6c4c42e09c1bf11", "guid": "bfdfe7dc352907fc980b868725387e9877a0940c4554e1e6ab40f12e20994def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ec0fc1360fd2d3c498462a89ceb84b", "guid": "bfdfe7dc352907fc980b868725387e98c4556563a0c6177db7c700b6cd038c22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e09d82b9f9b608ef37a7648c7a27e3", "guid": "bfdfe7dc352907fc980b868725387e9863ab3dac9d9058cc2b0da62010ec0979"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7eaa8738c9fef236786a79bd828eed", "guid": "bfdfe7dc352907fc980b868725387e98aa673bebda08b89ae20426fab6475b1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951cf83577682ba564716ee13f3ff097", "guid": "bfdfe7dc352907fc980b868725387e98b154ef234abc646177209b23a9a04b2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835396be31514bfb88afd70437423553d", "guid": "bfdfe7dc352907fc980b868725387e980327e281d6dcadca35cddc6fba92f5fa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616a32d21927b1d225ba3fe5382ba8ee", "guid": "bfdfe7dc352907fc980b868725387e987c172369e9a44a07d9096f8953b55669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e648606f64090de95f90e27531994b4", "guid": "bfdfe7dc352907fc980b868725387e98490649709743ee613dfedd1d3f318b3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f069c363c94a12480c3bbe982d65a4", "guid": "bfdfe7dc352907fc980b868725387e986e0f3f4d05628ea164aa29a9e57f9fae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcdab6757493799bc97d71457fa8ef1e", "guid": "bfdfe7dc352907fc980b868725387e98b2c8688fbd496a3c1a8da21e8e278c97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a997c1110c1a91990db5392ac2aaacc", "guid": "bfdfe7dc352907fc980b868725387e983da198753df047b3e990db15cc344160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834ee4bff2764bd6652512ad182630aaf", "guid": "bfdfe7dc352907fc980b868725387e9829bfa2205d18532e59f402b83a6501ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98049072044c6dbe3e01b34c436ce6d318", "guid": "bfdfe7dc352907fc980b868725387e98fb3abfd7b9b905d9d8af4887f016ecb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cb5b8b55629df691d64934d375f935", "guid": "bfdfe7dc352907fc980b868725387e988e7136b60a3fd2e2ad3136e6743d4019", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8b9d3cb7b1cb8177edd458d200457d", "guid": "bfdfe7dc352907fc980b868725387e9810b391a211eb0cadc6be6c87ca3af060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b96fc837d415937609783ebda353e2e0", "guid": "bfdfe7dc352907fc980b868725387e98a21081e62f5f654e91f775c58351dd7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1285001574cc91a3e4e19c90898feb4", "guid": "bfdfe7dc352907fc980b868725387e98ab452ac06898a450bbbf126f2b331d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983eca5e2abc9f9b4e082e3be2660da7a3", "guid": "bfdfe7dc352907fc980b868725387e9813d4fee49dfb0e7f17c33d522cc88abf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e93639240c460cebbd82882a068c1fb", "guid": "bfdfe7dc352907fc980b868725387e98a8ef92619994cffefe8d18804e622104"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277875c1f6e617ab1307413d4ba09efc", "guid": "bfdfe7dc352907fc980b868725387e98be1c2c681a2d7f8e92d341ada187a60b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895b187f29a5ae36f59b72a65895bfc94", "guid": "bfdfe7dc352907fc980b868725387e9810016b943305981ed8f93b8a0fc591a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0aec1823aa2d533ced06de8c1bb399", "guid": "bfdfe7dc352907fc980b868725387e987cd22e35061d8ad7bfdb018697a4edd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a1ef7d2a879b6c968bf7457709de85", "guid": "bfdfe7dc352907fc980b868725387e98e05a2a81170fdfa1d69689ab0ea06474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ba40ebbd3cf471dd4839dd043153b2", "guid": "bfdfe7dc352907fc980b868725387e982691b20e7b10509d019963493091a2d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d5db2357cf5324bc69ff99212a3e293", "guid": "bfdfe7dc352907fc980b868725387e98f061056b1a207e40785162dbaddf5162"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f069a1bd67912108dc730889a2a3afa8", "guid": "bfdfe7dc352907fc980b868725387e98a271c5d730e51484e59e2fa6abd206f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bab2cfa00f5c556bdc4388fbd923660", "guid": "bfdfe7dc352907fc980b868725387e9837a57aabc2a09bb0aedc4a6377950ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d181d7c72e25d7eafda01efb0abd52e3", "guid": "bfdfe7dc352907fc980b868725387e98ef60103aebb0e56ed190e09a2f6c59db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851525a1225bd12f9e6ca80e08a76bae5", "guid": "bfdfe7dc352907fc980b868725387e98f8601caa9db107a97a0066f6881326c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981708e93ad408c0f0d8daaa8d129b8a47", "guid": "bfdfe7dc352907fc980b868725387e98a18c36436df5a9eae32b5ba9be1504b9"}], "guid": "bfdfe7dc352907fc980b868725387e98c63e6dccfc3afe5451100f1bb6533d04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988a783e7a2713f69c95879d3f00cd6a98", "guid": "bfdfe7dc352907fc980b868725387e98c7ea483d60077df3c2364c8d2bf8e998"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46acea8f5692b4708719970ae0dd5f8", "guid": "bfdfe7dc352907fc980b868725387e988d59373bc65cf055044925f01cec14df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be14ab98918e1d2bacd471df53a2ea17", "guid": "bfdfe7dc352907fc980b868725387e987a2be5488d495a9854bee23378988b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98450fd7049642690c248f45f84792d2ef", "guid": "bfdfe7dc352907fc980b868725387e9879c4798e198acd5f3091d0355ce347cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807d85ecccbe95b1856f43166eb646940", "guid": "bfdfe7dc352907fc980b868725387e9868a2da2f7c61c79fb50fcafccb75f08c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4efb9bb9c5a9552ea2b700af0f728bb", "guid": "bfdfe7dc352907fc980b868725387e98bd570537ea2894fa0fb03eeba80eab8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f254a68a3119a79ffc233223e4f9c36", "guid": "bfdfe7dc352907fc980b868725387e98ac52a4bc76dde51e643f39f6c67d8460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e0f492e85e725b2bf6ad4647fed612c", "guid": "bfdfe7dc352907fc980b868725387e980d5543e4722dc5b7b16ebc7bf95d445a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e6cfc4c402ea70f266ec2207da2e5b2", "guid": "bfdfe7dc352907fc980b868725387e985a5180f4d6475370ed4d690336c24ff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a67e6f3e7787070cd0932d090c83724", "guid": "bfdfe7dc352907fc980b868725387e98bc8deed806da22828014978c05deeca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bca04edca6e8a9ce8d81890fcc86df0", "guid": "bfdfe7dc352907fc980b868725387e981454a0cf96e75253abed9434259106a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c262a9e9b705dc921db6ef395b4d01a8", "guid": "bfdfe7dc352907fc980b868725387e9878363f2c6f728f593bd458d328e41219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bfba979f10066bac81c3339fdaeec5a", "guid": "bfdfe7dc352907fc980b868725387e981169627c9b050186a0cfbe4aaef3c731"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1ed4d21ed28d9c8305017726e13ca0", "guid": "bfdfe7dc352907fc980b868725387e98a09423d672270ed206f6cb23d6b6d793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872c4461f1627c058401d693e2733f97e", "guid": "bfdfe7dc352907fc980b868725387e982e2ab206485abf37c280c3800aeb5a8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8d61c2e759bce8f42f934a47227cbb5", "guid": "bfdfe7dc352907fc980b868725387e98f2d1e0fb946f76ca532c895062cf7e8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6544e02929c0a47f17998f5a8b4415c", "guid": "bfdfe7dc352907fc980b868725387e98fd5004b07fc914b88380a5a8f99767f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b6c6026ae4e21e07117db5d9a9226f", "guid": "bfdfe7dc352907fc980b868725387e9806e33ed54f099b69f6e11f5b78aa8f00"}], "guid": "bfdfe7dc352907fc980b868725387e9860c4f68c873d991fc27f328d6095cf24", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98675e0f2382e4fa317b5e9a721061acda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e98e7a288674850f12a6790d6e64c9ca259"}], "guid": "bfdfe7dc352907fc980b868725387e9820ef5008bf454101b4e5a4cafec39723", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c57ace9389b1884165385fb731b182a0", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98d579b1f06cb8561675ce3ef7d7aafb86", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}