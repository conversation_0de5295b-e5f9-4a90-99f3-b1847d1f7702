{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f734896dc657487ebaa269637e0f73d2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845864c20b50d7bf61c87c9928c17b1a1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a91b14546423c8d344a6b404f477f6fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c4b231252ac0d65a94af0d73ff89138", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a91b14546423c8d344a6b404f477f6fa", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab52d681ee1a5ef337e0a85f4cecdb56", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9898965419809f495d4877382b1f49f30a", "guid": "bfdfe7dc352907fc980b868725387e986b66708e76a7aeec58a3c5cd8f184847", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a99b9ad9a6dc697a3eabacd5e10d5cc", "guid": "bfdfe7dc352907fc980b868725387e9829436340f865fbaee1a2addca601a8a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168ef23746a8e81cd6d1b26867d2df9c", "guid": "bfdfe7dc352907fc980b868725387e98c9e2c4efd25407ac2510891be268133e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d198607de74f5d2cc02b37ad1dfe3767", "guid": "bfdfe7dc352907fc980b868725387e98d6331c98705fb51e89e8690e49617435", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e769928b9dc520ff6ee614330ddc437a", "guid": "bfdfe7dc352907fc980b868725387e9868f7f841100092303f34f1a7aeb19b4a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e5e014c141e4222572f00fad1f82ad", "guid": "bfdfe7dc352907fc980b868725387e98812e7163e86cc98c678ac2c3e29c9caa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ca5c812e59cb2563d802d8c3ee22fc7", "guid": "bfdfe7dc352907fc980b868725387e98a856e4de9dd1b7d8da9ff15371de7edd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9025b946a7aeeaafdd00defb733a0a", "guid": "bfdfe7dc352907fc980b868725387e984eb613c54843126c6f78af53b910c2ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983470491b31480ff840592229770b0553", "guid": "bfdfe7dc352907fc980b868725387e98e79a59b7957289608ea6eeaa0294b244", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7c2f031ec890a6b10b2921f8bcc88b6", "guid": "bfdfe7dc352907fc980b868725387e98f4af8c57b8b8ed0d525c43cffd35f5ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa2afcf3ea152d42d58cc35cb393c90e", "guid": "bfdfe7dc352907fc980b868725387e9842f54261fc44cbff2c665a42ecf8c157", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98baa0e81775f622f0a2bc9e1545cef712", "guid": "bfdfe7dc352907fc980b868725387e98ef55e4629e2596ebc425a8f7b8097cff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4d1224c9d46e52b65e764c26709ef9f", "guid": "bfdfe7dc352907fc980b868725387e9811185eb8961f0a65012b48b5c524a5e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbe4bfd30e96041bee090f0403523dec", "guid": "bfdfe7dc352907fc980b868725387e9814eb62c5e35102ceffa5da14f771fa19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803bb1dd48e532bdbb72555eaa873e191", "guid": "bfdfe7dc352907fc980b868725387e9822c990070074a56c04bf0757b971ed6c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98405e55771c7756f43bda00f481776f8e", "guid": "bfdfe7dc352907fc980b868725387e9891d5a139f59472ec8279818ed22e9b45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f009ae309ed98363995759b313a09c26", "guid": "bfdfe7dc352907fc980b868725387e98513854531e9d646a66bbb9568f9866a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98201de2e5de5f3b25ab3372cbaf3d06dd", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8f3452eab1ba0ac3a8f93a6c843e61a", "guid": "bfdfe7dc352907fc980b868725387e980509f611b7a1fb6007ab7210082cbc64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe38eb216f20f4ff0760e21491004b9e", "guid": "bfdfe7dc352907fc980b868725387e984842508da48a518a2115ea015f42d1de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835c2b3cd165be57722c517849fb317f0", "guid": "bfdfe7dc352907fc980b868725387e984a087b41025f3da30bd4cfcbf74ca818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd74df57cccbbeb259b70a428f73e2e6", "guid": "bfdfe7dc352907fc980b868725387e988eb65ad19091fce8bd77ee4266be27cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879ad0bda7d0fb82fea9a8773aed87b67", "guid": "bfdfe7dc352907fc980b868725387e98b141aecd7e467ee586ddc58c27cadc85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d71ed690dcc231755b5bb829c8b9ff4d", "guid": "bfdfe7dc352907fc980b868725387e98ff414b9e07ba41e1a860d5d1f6c91b9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808e177b76e2d748acb514b5aec146902", "guid": "bfdfe7dc352907fc980b868725387e986c1ed53068661206b8ece6087f1ce4a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980591c72d2103f0cf62ab9f01d157dd24", "guid": "bfdfe7dc352907fc980b868725387e9880d3ceef880c41f91d78692de646b75b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818628d981b6fdf8229d1cbd507fa952", "guid": "bfdfe7dc352907fc980b868725387e9837354c381bf545b5f188450d81aa9767"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bc2ce0f60d9a99917003d416efcc5e3", "guid": "bfdfe7dc352907fc980b868725387e98bbc15aa14b775419c902350b62d2de65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22e18563916925b764e46c143172b94", "guid": "bfdfe7dc352907fc980b868725387e98baf8e52502c280820c35df945901c814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d775846384289801612d7728838ba6", "guid": "bfdfe7dc352907fc980b868725387e9825c4a4fdc5bd01d05f0fff91bb799c4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed068b4fa27105eb47b54f7ac7f5c1c0", "guid": "bfdfe7dc352907fc980b868725387e982bdf1a3d25ac1aca2eb0b8a9ed93baf6"}], "guid": "bfdfe7dc352907fc980b868725387e982ad3667d1f3573c96d2a2e5ae7fa4bcb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98abc643bd218bd44c774ad67fece38daf"}], "guid": "bfdfe7dc352907fc980b868725387e981787b72bb159c812f5ae815acc704875", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9882d28e4c77380c1eac2a6c8d30e66314", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7265512b31d58a201289f7f8dd4455", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}