{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d93dc7a2040033fd111847d7418b65f8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980cfef22af0d11803527f75ad338cd008", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98074133fd9271f7abf97ad81b83df1a4a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989440beb66622d56b9ccc86a11f33dd37", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98074133fd9271f7abf97ad81b83df1a4a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheckInterop/FirebaseAppCheckInterop.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheckInterop", "PRODUCT_NAME": "FirebaseAppCheckInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98812511968b4038da03e4a7dd402be061", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d611078789452d689c68344d2489a208", "guid": "bfdfe7dc352907fc980b868725387e98e2b0cdcad0983cce5ab35dee48e2bbdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837981aa05cc6e16f265ca69e258538ce", "guid": "bfdfe7dc352907fc980b868725387e984a953907d05077ac50b406d60ddcfdb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839e6fd4029374e963691cd975f9bb889", "guid": "bfdfe7dc352907fc980b868725387e980cd0e4daf8508ed6b60e3ff3193179da", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983422699f696a6ecf1f9a9201b131d18f", "guid": "bfdfe7dc352907fc980b868725387e98905ba7c21d096d07c37684e54f64cd01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f678ecc6cf37f377203d2d69ac2893c", "guid": "bfdfe7dc352907fc980b868725387e9894f2a6d96036420b30fe366328f3ac9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0ae6395aee36c958ca74d155ad2535b", "guid": "bfdfe7dc352907fc980b868725387e989234ee02e0e4042d4b272113ad427b1e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f09289e5cb9c667bfeb8102ad64b2c68", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98782a4ca8ea5e25aa2030fa1405127a25", "guid": "bfdfe7dc352907fc980b868725387e9886f072b96f802d6949056a23ccdae7a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e30294d89b20e9799c16ebfc8508506b", "guid": "bfdfe7dc352907fc980b868725387e98b8d30d01f0dc76a1a79cbd216944d629"}], "guid": "bfdfe7dc352907fc980b868725387e98d253f2ce807e4748f8a2f6ab01bc3427", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98c9c0cbe52c4d1b43dd9ad1a022f6ef0f"}], "guid": "bfdfe7dc352907fc980b868725387e9804652e5e31041b83994f89dc5538b6f5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981919b1a20ce6f46162e049124b04b6f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982cdb0c7c817307e018cfb4299b646a42", "name": "FirebaseAppCheckInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}