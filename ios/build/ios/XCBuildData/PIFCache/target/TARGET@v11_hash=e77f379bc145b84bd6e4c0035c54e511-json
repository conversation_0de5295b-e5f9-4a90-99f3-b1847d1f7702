{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9807983c8a2f019acad3c02fef3317b167", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ba6fd30a2d2c0667ddeeb471b87f3c67", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984518f372135660482520cec09cd121b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985872c6b49f1d92292872024b383071ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984518f372135660482520cec09cd121b2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869811edde2fbec4b998cd7c872ea4b10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd1031f371f3591df0ec0742a46e288f", "guid": "bfdfe7dc352907fc980b868725387e98c2a4641ad923bd530986ed3b1808f38d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd4dfb5919c98ece97e0c7220388a834", "guid": "bfdfe7dc352907fc980b868725387e98064e5d9d447c45296facb08dfd22c5d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd7a1b836ea50ce905a2cfd18f5dfc9", "guid": "bfdfe7dc352907fc980b868725387e98ea572a5fe57c54b9ce702fdb6d0ff22b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e69c7ef29423acfcb8e57682c862aa69", "guid": "bfdfe7dc352907fc980b868725387e989b80c5ff99c75c7878b7f16c0c280291", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981511cd2d2a56aeddc58e87a188be87ba", "guid": "bfdfe7dc352907fc980b868725387e981e47427dd75a9f2dfd1b3a66e2e18bdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e9c6a6bc0e34204d96f7cf9e414500a", "guid": "bfdfe7dc352907fc980b868725387e98ff6730d2b35084eb750550ebb832f92d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98033fa3db9e12cd5af41fb9a1c684c0e3", "guid": "bfdfe7dc352907fc980b868725387e980fa2929dd4415b5fe1d7a8c39b748240", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45c5e23584703efb82fc6831c17c45b", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b5c2980c4193a7f3b6d61ac6eded9b", "guid": "bfdfe7dc352907fc980b868725387e9898d2edbab6ef487b5de7d9a2041a9a3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987178783c7e0676aec40b7613a1218d98", "guid": "bfdfe7dc352907fc980b868725387e985351bafb61c8b3d873bc09ec1dbb65b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dff69f6d83074c986eca3dd5a35c5533", "guid": "bfdfe7dc352907fc980b868725387e983704b1b8c2a1c92d7c707f7550a484e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f35a720b6ccfce7e9ea4891471abba", "guid": "bfdfe7dc352907fc980b868725387e98318409e0f3ada4d8f665c24367ac9e7d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d2d5b655b9d026d3cc5387477f0f7a7f", "guid": "bfdfe7dc352907fc980b868725387e981535f86d48a45b2747372f0e985bc0ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802311d84414e48178d0787fd25e21904", "guid": "bfdfe7dc352907fc980b868725387e987fc2a03e6257f2d9f073a02b14a93d1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981df62240cc619fb8e9dfff36b8d4cd81", "guid": "bfdfe7dc352907fc980b868725387e9832b09f7beb9cd94542bd8f3809e2a5dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c17b86ec7dae886a0a19942fe6f5e10b", "guid": "bfdfe7dc352907fc980b868725387e9807d7f744caa0474e111785e8375fd803"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98702522ab5ada0c5afd47f937875a7274", "guid": "bfdfe7dc352907fc980b868725387e987b0a8d62d3737f3056c41de2d78775cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c1c028539efcb2c65227210cd4c5ba4", "guid": "bfdfe7dc352907fc980b868725387e98e75737eef9c683e55c130b993923df67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f4bab5723fd6f11c29802657189730", "guid": "bfdfe7dc352907fc980b868725387e98cb5a47a9bd592614dae1404af40d3485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842d99dc50ed476d90cf0e08622616f56", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ce24547fa0f57f8d666bde98c257daf", "guid": "bfdfe7dc352907fc980b868725387e98159a145156f12c6c0d7afe63a7375400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989529e1dea2941af86a01e363b9889f63", "guid": "bfdfe7dc352907fc980b868725387e98e370ebab375c43506286367eb041fd9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6f313717841bd165837f3aaa8c3be68", "guid": "bfdfe7dc352907fc980b868725387e98adf9665a3f62fcc27cd307c736c70b07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98133f86726b71e016571bc3a441064f08", "guid": "bfdfe7dc352907fc980b868725387e9869457e72a9d1ca793d663187c477ff06"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}