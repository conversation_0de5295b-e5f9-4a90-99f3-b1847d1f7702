{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aefbeeef55c2a3d32fc1dec903243761", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896749afb9445f0bf137d0d37dd40f812", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894ab3643052d95bc3c430d4521ca7695", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fd4fb36166163962aa5dacff09b919f9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894ab3643052d95bc3c430d4521ca7695", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19731fd48e9be633adf5be9b19517ba", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9807e7739c73ca0654b3cc3b8a5fe07df3", "guid": "bfdfe7dc352907fc980b868725387e98c385d051f8d9e102b7dcd478e20dce16", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a2b6922e61e1b31b41c4435e44e9f8d3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f78c15e90afc982673d80d885088057a", "guid": "bfdfe7dc352907fc980b868725387e987fa3e81bc0e44c87754660e429261b87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d172046d317d37cecf8500947237e92e", "guid": "bfdfe7dc352907fc980b868725387e981e0d63d435d4627a0821fcb73d433c7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2a5dac68a966ccacc411b6097ce54c7", "guid": "bfdfe7dc352907fc980b868725387e98e6080e5ac010fa8e889e92cec545a060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc946b369432a4f1a4dc3ee87a15c7a9", "guid": "bfdfe7dc352907fc980b868725387e9823a810ef9113ac62e420696b1d4eb485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f996cc8061bf5a966d31923b3fc96be", "guid": "bfdfe7dc352907fc980b868725387e988c06f0f65436d5c896cdd35bac9464fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e872d8862cdd8c91daa9f299771c575d", "guid": "bfdfe7dc352907fc980b868725387e98999e604157668a61467436d0b8d0bc1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2c3db3af558c3f3072f4e82af478557", "guid": "bfdfe7dc352907fc980b868725387e98cc1244a2f1d33698435a05067b1bfe78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bc1885dc02f4f49e3161c5031d65223", "guid": "bfdfe7dc352907fc980b868725387e985d71e2ffbdce41c8eb7f40276fa5bf83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d689c17b12847f7b6666e1c462d6e14", "guid": "bfdfe7dc352907fc980b868725387e9895572b898edd4230614e690db0f4eb0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08ad191674c63ed8eb5a67c4898563e", "guid": "bfdfe7dc352907fc980b868725387e986c0938579c025915197a3eeb5c99e81a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bc8ee835ce7c58879abbc7be5a804b9", "guid": "bfdfe7dc352907fc980b868725387e98ada251ffb192de41e1ec1853cf7c2cb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f671971809007f895606babef1da1d8", "guid": "bfdfe7dc352907fc980b868725387e98e755549a76fafd6af9b8ed9c1cbf3ab1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98263e3a4981be781bd6aa1caa38a6a5e0", "guid": "bfdfe7dc352907fc980b868725387e98b505cd9b548fc50beff3ec461cf9aa68"}], "guid": "bfdfe7dc352907fc980b868725387e98966c6486fe558ff34b58a5bb121c4ffb", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e982664db3d98c1e56ddab84fb16a9f8334"}], "guid": "bfdfe7dc352907fc980b868725387e981610b6c41edda70901188c8a4e3c3126", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980040894ed77caabb1c47ad551176d895", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98d415afbc18eaa1ffa6bd20790eea66c6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}