{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98426fc13da00dd8250cb9aa4b53d38446", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849c05f8b3f0972bd5de3b045f3a1e4d8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fca7a0f89e41b77095b99dcfd0b0a3f2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817f399f5c016e3166a8484b1b21c54ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fca7a0f89e41b77095b99dcfd0b0a3f2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9875a70c3b6f7ccf7d3ed68dccd9e83f2b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981f40e5224266baa244d788dd09e950df", "guid": "bfdfe7dc352907fc980b868725387e98e2f9c9851469872a0edde1935478eba2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98552667ad5de4f9880076a6471c130169", "guid": "bfdfe7dc352907fc980b868725387e984b149c4194447d0a6b79afac57a9f8f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807b9404f8c098f576d068f56f05f02f2", "guid": "bfdfe7dc352907fc980b868725387e9893b49ae2ce1cbc194905d8bb61a2d34c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcd75ab1c10e284411bda80cddd56f0e", "guid": "bfdfe7dc352907fc980b868725387e98702c0024011b7814e9cb7dfd1c019c17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0d74e5d5fe60a79b5c00209aaab83ce", "guid": "bfdfe7dc352907fc980b868725387e98715ffa579f71d370dd6a5217d5d04d5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e03c771f4827f9caf1767aabe7c8e50c", "guid": "bfdfe7dc352907fc980b868725387e98376d80d28863e76f893e1df5be10f06e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ff75c5fd8eb2b5706fecbdac270812", "guid": "bfdfe7dc352907fc980b868725387e9844ec00135ed4dc04faf35d34ff8b527b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826761ae19a857c2fb3688874684c77f0", "guid": "bfdfe7dc352907fc980b868725387e98fd066064dbb764cb85b4eb315f2b5c6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d613c71f1e6ee6736793173bd94a1465", "guid": "bfdfe7dc352907fc980b868725387e98e1bc1aba0d927d16479b7d4be2107391", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9f05da9359870ca3567b9f43abc02e2", "guid": "bfdfe7dc352907fc980b868725387e983b1ac74b22767b78cf00281efd39ad2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982359e4094bdef88f5b0da7196c55c910", "guid": "bfdfe7dc352907fc980b868725387e98ea1980825625cd62e8ce0ddaf6a5dd6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b63dafa5b9d6a81f2a93f2f321e87f", "guid": "bfdfe7dc352907fc980b868725387e98ecb027908ee13b1a5043fa5bd9b5aec1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98500c23f5b9b07211e30ae770a26f87ca", "guid": "bfdfe7dc352907fc980b868725387e98f94fe9576f4cde2b8d48e022c420fa07", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aef60053e21d963f7bb4149205cdd766", "guid": "bfdfe7dc352907fc980b868725387e98a4b08b579066ff8928e6e9e984a0c223", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6064d3e14f432ace6d31bc5321600d", "guid": "bfdfe7dc352907fc980b868725387e981f38d868cff9ae5a27eba97b9e337933", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a89ed1d4cb5870188c95022f3f591c3", "guid": "bfdfe7dc352907fc980b868725387e989277b4d31d93b27485df5eb3d612b641", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8513f463905f646db6437221dcbb39", "guid": "bfdfe7dc352907fc980b868725387e98a2c2537514fa8666cf1cffa08063eec8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843767efa63349e102311b68aac98b19f", "guid": "bfdfe7dc352907fc980b868725387e982a6be0cda2d42e0b42be2b21d7403c0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c7e5bf248815678f9437cad92ab76e6", "guid": "bfdfe7dc352907fc980b868725387e98eb7c292f097c007b2ea4d290cce384b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5d86f3ea115b3d3438ed33d70cf450", "guid": "bfdfe7dc352907fc980b868725387e984e37895c955ada219ff18ce1ebdfccb1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f3f32932a1543425fe182b7097c2159", "guid": "bfdfe7dc352907fc980b868725387e98220b422d6b62b1d2e5e2fc7531fde938", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309f2f589b38fe54c071bd28a4c4a3b6", "guid": "bfdfe7dc352907fc980b868725387e98f10b1381edc80b799f76e00c5b1fe887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f6cccfc2c84fc8f8a256a79140ae8c", "guid": "bfdfe7dc352907fc980b868725387e98ad2d78c09ff78fa69998c78feb665c94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9176c8235d296f4bae92f0ce6ab31f2", "guid": "bfdfe7dc352907fc980b868725387e98c66ee2b8d2c583b69e2eda61d91d6b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56311d913bfbf026144281fb86d07af", "guid": "bfdfe7dc352907fc980b868725387e98f70dcf4f6431a099ca8c73a6b9aa86a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e8b3cbac99dcb2196660eb9f014f95c", "guid": "bfdfe7dc352907fc980b868725387e980e605ce3452fba10f848802813155d8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaddd8f5f516364af495d8dac4a07175", "guid": "bfdfe7dc352907fc980b868725387e98c161d8d0129c87ba3f5be61f1c7d5df1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5d446481d212cb7b6dc27796d038ed", "guid": "bfdfe7dc352907fc980b868725387e98e16b932a65a9848d28b014e9c3f727b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46c1f1d3c2b9d7c6a2969dc71f3e535", "guid": "bfdfe7dc352907fc980b868725387e98f6b0afbf0de829ea659798e2e9788da4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871565ab50fe121199d9fd64d195d33fc", "guid": "bfdfe7dc352907fc980b868725387e9880896ab0070e82955341ae3c67b1ab6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da3d2051263b5fd32829a4f5544a6f08", "guid": "bfdfe7dc352907fc980b868725387e98f42a8bf03ae6b12abce2328bf3049149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984285574ea97027d3429dab3cae48a963", "guid": "bfdfe7dc352907fc980b868725387e985ac269d1acb3222a91f5898c615d4b85", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ada77337ccfe1c0209616d0642ff18b8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b103262bedc3199a19639366d49ad323", "guid": "bfdfe7dc352907fc980b868725387e9854537131d9432f7c6e8b5564ae8d6192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9d33825b46c3ee3b75f0e9059e2f5e0", "guid": "bfdfe7dc352907fc980b868725387e98248233ea96a362a5cdac53880ad2242a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd4a5623ebe605b4124ecd0bd820676b", "guid": "bfdfe7dc352907fc980b868725387e98270a85a2ac7f9a9309657ab963af2d6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d1cd26543b522fb372b95c62a0ffada", "guid": "bfdfe7dc352907fc980b868725387e98c2e626568a79047e9042f3e537a3c8cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84c3c2cfd482cac4b4ec8d84acaadba", "guid": "bfdfe7dc352907fc980b868725387e9878a3aa7f53ad56c1033896f31bd06dd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da67625a716b79d8dfd33b6fd52cc84", "guid": "bfdfe7dc352907fc980b868725387e98e2664ffd9cf41854336aa258743e89f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ceb11b5458a40ed35174f877284b3e21", "guid": "bfdfe7dc352907fc980b868725387e98b3f5ae8b5667876aceaaf3fcd4c378be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f9ff5f07c65ce6d521d53c0473a2fe", "guid": "bfdfe7dc352907fc980b868725387e98b4a00f93cd798bf5481b1ef0acffb349"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b12f002bf0e4fde7e2dc0b15d1a1f0a9", "guid": "bfdfe7dc352907fc980b868725387e98e6124e3b05f058dc6ea88ee50c3684b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e07fc6c194539c9cdd061a1b8dab60", "guid": "bfdfe7dc352907fc980b868725387e98306f727713cbb7f35fc0a8f70a264b49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e446bb813d6b2df4de261429623ed5", "guid": "bfdfe7dc352907fc980b868725387e983515a47c68caa2ce0cd508e208eb8660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddcc7de3604edcfd8fc3ec45a1d9cf8d", "guid": "bfdfe7dc352907fc980b868725387e98cd8583331d06cf03cfcbe7c65a66c83e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd4add24a68d73f4ac60173d0e886ce8", "guid": "bfdfe7dc352907fc980b868725387e98cc3deeb9a9a306774301d27dc248271a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd6dcb4bfaa5e0f3bb381d415fc30185", "guid": "bfdfe7dc352907fc980b868725387e983a94d00174211ff86c11c68aebf7ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb29d5428800fb10030001c1a34333cb", "guid": "bfdfe7dc352907fc980b868725387e9844fdbff6b242c830493beb6b2ef06c24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae29870899d198b23304c4a9eddd93d", "guid": "bfdfe7dc352907fc980b868725387e9814e1774744c884766970f0a2efde8ae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98804c69a9decf5c9c40e190620ff5c56b", "guid": "bfdfe7dc352907fc980b868725387e98314915524a8377522b9e652e6165b299"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b4d8659718d22f5992f437def24151b", "guid": "bfdfe7dc352907fc980b868725387e983430445522ba512fb23aac3b66765995"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980490fe5982a48f516743d95e9011d3ea", "guid": "bfdfe7dc352907fc980b868725387e986e4b6ac301da5aed585707304d396c93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb2ce44c5128011956340845430fcc04", "guid": "bfdfe7dc352907fc980b868725387e983e03a214c64fbb95e4186e6eb72d8b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801d4b04f24b7580fdec617d2111ed3c6", "guid": "bfdfe7dc352907fc980b868725387e981d0fa5087e70e15ae2e2861686afe70e"}], "guid": "bfdfe7dc352907fc980b868725387e98adfde05992d42cc1f4839f7fc5889bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9838646818c8325b2e3e81ef1984e7a1b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e988313b32ccda5faf658b7129ade9408a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e98dcc00355db6d954ab1dbbb3ccfd1ae42"}], "guid": "bfdfe7dc352907fc980b868725387e98ad0c5e7dd24369a05c29d188a0a88c9e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9852e9d36f427a3d275239fc1041d41d27", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9808a2edb48ff00568fc0e8c6fe4039fe0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}