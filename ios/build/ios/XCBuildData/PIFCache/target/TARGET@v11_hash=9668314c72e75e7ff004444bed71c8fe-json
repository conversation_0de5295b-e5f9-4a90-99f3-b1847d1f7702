{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa511d4e2c2d64de95e409021b395882", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2277be28a487919925c019eadbc3d4d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981255597d6cc02ea8920070e906f7f519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837d6095d2edb1b19ee11d5bb60efcadb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981255597d6cc02ea8920070e906f7f519", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/rive_common/rive_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/rive_common/rive_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/rive_common/rive_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "rive_common", "PRODUCT_NAME": "rive_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98266c1528ba49ac2bac63a560ee690878", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9808f49f18f4d1e28150c151b256ecac02", "guid": "bfdfe7dc352907fc980b868725387e98e266072598b9c98e52aea7bbded35295", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9800d50e50f4ef8e57b903ef311be596b5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e8c8ab3cc3dae6eb13a1d3245f477af0", "guid": "bfdfe7dc352907fc980b868725387e98f956606919a70328b673f2811dae0f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870de620223af8e657c73be512fc6c062", "guid": "bfdfe7dc352907fc980b868725387e98e0469c2fec65e21dfda886b7af8481cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989469e003e93d6f756acfdb8f8cd37fbd", "guid": "bfdfe7dc352907fc980b868725387e98528b993bbdb923a00508365b3cd419df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98336cf763c1968487bfd5d68a39e01948", "guid": "bfdfe7dc352907fc980b868725387e98c60424ea8f67c04a5916a16d6dd94796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987274670dba1a3403bbb2e4d647f22509", "guid": "bfdfe7dc352907fc980b868725387e98c0a34795ea2244ebcdf71b0cd93bd0e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98123b6ddf20ced1c4d1f58039aa380a96", "guid": "bfdfe7dc352907fc980b868725387e98009e148e96e69dd51a553cc19909f245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98458dd233669f302a045aa2fc4319df51", "guid": "bfdfe7dc352907fc980b868725387e98a82eccfda439e6633d724dcb50726515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988587f48065ec14b2b99f7ec20f4bddbc", "guid": "bfdfe7dc352907fc980b868725387e9810268ddf0e42bf6751271dd1390935c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c77df1e4c677d95878fb45510401beb", "guid": "bfdfe7dc352907fc980b868725387e981f5358d51253c54a570d14c23ddccee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1d261adcd2aba0081f6cccd225fb8d9", "guid": "bfdfe7dc352907fc980b868725387e980cf71455486d10d202f4bb7e91beda0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98601b5abba5dce59eb9be9ca02ae1a9c8", "guid": "bfdfe7dc352907fc980b868725387e98f192690cb6ca684a73b833369a82f010"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984393037f78428e93ccd96439eec97743", "guid": "bfdfe7dc352907fc980b868725387e984fe8f197a5ebdbd8246f78fb0308a3e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c46d3f7dd893d91d2af076d502063e6", "guid": "bfdfe7dc352907fc980b868725387e98d858e7cb905f99090a3e43f1a50bd719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880ac65324c9cf1a24cf07217afb6fc48", "guid": "bfdfe7dc352907fc980b868725387e98f540e5bd34d742ba7c74005dc1563486"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d387a864c0a056636f91209def9f70", "guid": "bfdfe7dc352907fc980b868725387e989d3419a678dd33e3029c197704e7e574"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf87fa3e7c8408804d3c7ebaa530cb14", "guid": "bfdfe7dc352907fc980b868725387e98b7dfb5a7794aa51a40a2dbcd5d9ae368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91832e5349ecee4d93b60f7ec60c629", "guid": "bfdfe7dc352907fc980b868725387e98ba80ce86de0d715c37e6ef818a74dfc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896652ed01baed6967102e5599c888cb5", "guid": "bfdfe7dc352907fc980b868725387e98652777041c146f10b2bffb99fa80e10f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6e5a5a7de3e311a365a586ab2dfa649", "guid": "bfdfe7dc352907fc980b868725387e9859c06fcf360356415472bf797636987d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae090a36e16fe06cbd09fc773fc4f9d8", "guid": "bfdfe7dc352907fc980b868725387e98699a820ba32ecd61a3f069bcc21f2423"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf682b9364c85e8dde47d74f2b229d5", "guid": "bfdfe7dc352907fc980b868725387e98a0d1c5f558618a5a44935e6cd41ed9a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989777661424af50e2814c5bbbd47e5fad", "guid": "bfdfe7dc352907fc980b868725387e980b616ca093d7ed55edacca4090e045cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a659ca21dd75b4b58ce134e473a8c206", "guid": "bfdfe7dc352907fc980b868725387e981edd502a79ebc47e82a88713a9802cb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9fadc686b673817e7cd934e56732347", "guid": "bfdfe7dc352907fc980b868725387e981912e5c0584b58cbc5ced95ad87e9d13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839fcc2329368a55aa61cab874d2667cb", "guid": "bfdfe7dc352907fc980b868725387e983987ffd340406a3ae923edb8454cb4ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ad85edd24233caa8c6b19fca275f83f", "guid": "bfdfe7dc352907fc980b868725387e980b10de3e5caa0dffee27fa9900769cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983af6ad5b72dd5b1f102741d5816f9540", "guid": "bfdfe7dc352907fc980b868725387e98ac51350f99deced4d46475346376b5ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f31db28fc52fba97a18c6abcbe58d75d", "guid": "bfdfe7dc352907fc980b868725387e9801a124726b99ba969fb99b0d106347ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9f43a6ab963be2d8f32f7bb1900c3cf", "guid": "bfdfe7dc352907fc980b868725387e98dfe3f15ac94b09aea6c9a2c0eebc5178"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d7a1d19da4e458a0c9a4e8e0146eab", "guid": "bfdfe7dc352907fc980b868725387e9871928ab9c135e60546923ae23c12be67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817e3378f0cb76c9f280fad61381fa62e", "guid": "bfdfe7dc352907fc980b868725387e98cf8dd9a92afacc2af8503020da6d5aa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98617b586f4d579e87d31f12ae99f80f34", "guid": "bfdfe7dc352907fc980b868725387e989c6b6d7fd406bd16f91fd3af5ba6a8d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98686565b71a4f963b8d9223cd00c9c9cb", "guid": "bfdfe7dc352907fc980b868725387e9899696a79aa5b4a5638769b80d45c4f51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826465d87278be25deb41adc6cdd15885", "guid": "bfdfe7dc352907fc980b868725387e986466e054e87f3b91f54452a587437f4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868681f8831e9516bf73ae7ba20b03285", "guid": "bfdfe7dc352907fc980b868725387e9829fcd5be713813fe4f039d31367fcaef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e215316b7520c9fc1f9bed0beaf6bf9", "guid": "bfdfe7dc352907fc980b868725387e9885636b8f6d7bfb7aea61ea535b2f67da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96c1ce2b61671d57cc9d7cd7bd70a18", "guid": "bfdfe7dc352907fc980b868725387e98dde4f475a0217689214aa571c5392d41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e31ee0082069a972164f2f754944ddbe", "guid": "bfdfe7dc352907fc980b868725387e98de03d3ab1703a3569234f26366ca725a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7b3ed66a13e0feca4106e287a40d009", "guid": "bfdfe7dc352907fc980b868725387e9836b1b2e84ce91b0c08ab36036ae296c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb969454a1ca910e7fca6d949fd9a3c7", "guid": "bfdfe7dc352907fc980b868725387e980de0a69ddc5cc7afeb29a2134a3522eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7b8cc9e6bd53dc40342b1732b624578", "guid": "bfdfe7dc352907fc980b868725387e98483976b4ea062e23e3ea6ea6a076c8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa8bad2d073cd3c2c3d35c32fd7e3243", "guid": "bfdfe7dc352907fc980b868725387e98b6f71d457954a0c4aede50f97e3955fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d60b61240a949a1215ce2cb19112222", "guid": "bfdfe7dc352907fc980b868725387e9824d0d5ef8c214f17b84b631f703f2c58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b403980762e5c366bed8082da155f8b", "guid": "bfdfe7dc352907fc980b868725387e986bf818bb394870431faf4dbbd2e82915"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ab46b17f93f225031e60e07f449a311", "guid": "bfdfe7dc352907fc980b868725387e98017564806179401143bb128266fa018d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869d969cc325996400a798a34df70eebc", "guid": "bfdfe7dc352907fc980b868725387e983293d4d143b5f13972451f144da18088"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ca4d2bd0ac358c9b8a640dbb42ec8f", "guid": "bfdfe7dc352907fc980b868725387e98652d98ceef003b6ac439d26a28ff86d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a21c8c0fa5189cff615974bc651d11dd", "guid": "bfdfe7dc352907fc980b868725387e982351f28a0d304a9ab4a0e9bd163b2c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d03d9750c386d0484ad529b5527a75e", "guid": "bfdfe7dc352907fc980b868725387e98e3d88937d43c27187dc2360a1233ecd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d41b4639b2c074b07cebc366dd6d394", "guid": "bfdfe7dc352907fc980b868725387e98a79946a8a8c833ff8fac81b86cb287f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c32a50887e2ed001e628312b4754fc", "guid": "bfdfe7dc352907fc980b868725387e9897e12f5435d46082bb708869b07dcbdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f9e773bbe502ace9d54c5bed71e274", "guid": "bfdfe7dc352907fc980b868725387e9808eaf57d1c13e8964924e75bf8eee99d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801c212425d36c58e25490f9da3d964d7", "guid": "bfdfe7dc352907fc980b868725387e9846969095f3b399722d3b05b98a09298a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98676169148673e192e8778eadf3ae29de", "guid": "bfdfe7dc352907fc980b868725387e9876418137764a27a2986792590bad40b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee6a2f50f5309b9c3cf1eb0c35ccb21", "guid": "bfdfe7dc352907fc980b868725387e98afda8907e71ec6839648be8a0235902e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d67032484e47a4ff7e9b5d8ac30395", "guid": "bfdfe7dc352907fc980b868725387e98c739e5f26c381e9534a3948e7390fc92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41a3ee11dccc74b2ffcce7372942581", "guid": "bfdfe7dc352907fc980b868725387e98d9da71e4b4f2c1bf1c56ca279a38019d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd323690ecc2b9cf548aac6edc072e2c", "guid": "bfdfe7dc352907fc980b868725387e981947d849e35db3d61e56adfd0e59abef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff44e9f6b133bcbe188ec04b8ecb03c5", "guid": "bfdfe7dc352907fc980b868725387e98d4c69daf7400ead3da17c474fd2a3ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d578a9026b2a1ca4b53c566dbb5dfe92", "guid": "bfdfe7dc352907fc980b868725387e98cebc6f66a5ba0e61ad41f1f0a36c37d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821561ddce89cab101bba94e3444aaa33", "guid": "bfdfe7dc352907fc980b868725387e9816b3cbabecb4e2475927f00b0c668f11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e20e0e8b0cbef6116bbbf993d5b91c8d", "guid": "bfdfe7dc352907fc980b868725387e98c70ca0dcc66f1a4fdda2552c65d66b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac5c7e982c2e3e6305b83179e56c2ad7", "guid": "bfdfe7dc352907fc980b868725387e9897bfd143b38d893f55b2593526e7bd1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f6e02be01a65f14d80b127d3089fca3", "guid": "bfdfe7dc352907fc980b868725387e981fdbd82eb655554e23450f9f0636b08e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362bece33e4fd5e426c565d340998ef0", "guid": "bfdfe7dc352907fc980b868725387e98a315e7c2756884d814209a7060a49975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98547e4727bc77375c74eff33be1dd8556", "guid": "bfdfe7dc352907fc980b868725387e986af6ee73bc103edf808d7841859a8a56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c990247f18512c7e449ad15c8e4fe56c", "guid": "bfdfe7dc352907fc980b868725387e980ed0b8a7e60ce6effd37f576cd426899"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820132d7723853e36f3e09519b216652a", "guid": "bfdfe7dc352907fc980b868725387e980de377a4aaf1f9e3bfa053d76c2ef29b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98067b83f35cd2ace95440a57a0f41b13a", "guid": "bfdfe7dc352907fc980b868725387e98a7dab654630c454df2fa2be6c9d69464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d45ba6b9d8cd41869681b803b969e3", "guid": "bfdfe7dc352907fc980b868725387e98faf6c37f3ffff4f8501e2c70cc894742"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aea0cfd974046bd3ee8bff39edb80d40", "guid": "bfdfe7dc352907fc980b868725387e984e958cfbd21d3636970c0e73036b6075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98285b0da029d7250e7cf94dd93524511d", "guid": "bfdfe7dc352907fc980b868725387e98eb0268fdda3ac76feaf00f6acf0a7a6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0cb7bcf2669dbcb0132083c52fd8a3d", "guid": "bfdfe7dc352907fc980b868725387e9889a472f7cfa77e2e09606cbb638089d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d396aa6524231c314e08b055efd227f", "guid": "bfdfe7dc352907fc980b868725387e98efcf2d31feac071ce70b73c5d71e5b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98593f95c3709e424d292e8fea76436642", "guid": "bfdfe7dc352907fc980b868725387e986448cc18fccf832b75437980db5a5e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893f8d3ed25b1fc86fc6b4ae8a4459799", "guid": "bfdfe7dc352907fc980b868725387e9829301253e813148fac7a06a91a852ee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ec0984d2cd725f202db8903f140ff4c", "guid": "bfdfe7dc352907fc980b868725387e988161d46655a7ee502bd1853d24250492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d1d7372fe2ce401dd02cd922723adbf", "guid": "bfdfe7dc352907fc980b868725387e98256fc9952435bfb0e807cd0a4138944a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d95e5057f19df8672a59cf8df26e714", "guid": "bfdfe7dc352907fc980b868725387e980b0f32d6d99e0ee54d6f32a6e1c49dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b05983a821e867bd55b4f04e89f6c887", "guid": "bfdfe7dc352907fc980b868725387e987c560d0237266910aeaf3edfc132a471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983643ca1875418e8e24abee6bef1c5543", "guid": "bfdfe7dc352907fc980b868725387e9856a5612f843b66c325532a502aa6ca41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cbfab240fe225252c35faba204a85c9", "guid": "bfdfe7dc352907fc980b868725387e985c0742d440caa66d2f725c00ce102fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a398c9045da9331066711e98c52d43d6", "guid": "bfdfe7dc352907fc980b868725387e9844d91146e8b35c33290781801192bf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d53ab330e2fbcc5a60a70052546dd9a", "guid": "bfdfe7dc352907fc980b868725387e98bb5790dc6ad7b2fb0291e496cf56d1f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bec78a401add265801ec381f35f655be", "guid": "bfdfe7dc352907fc980b868725387e98e24b5dad36dadbf9e9c04278e6d35ffe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a7a65b204e5adee1bb72c23855d013", "guid": "bfdfe7dc352907fc980b868725387e98291f05b72c67d61082affaba391c3748"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8fb60f549142cf4f5f6567b6e2b1e49", "guid": "bfdfe7dc352907fc980b868725387e98a89f73009822f5d9b8d47d716d7cd032"}], "guid": "bfdfe7dc352907fc980b868725387e98997d1f3320b96a489bbfeca42b5f2b52", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9820ad3732878a4721ac229b7e03e0956a"}], "guid": "bfdfe7dc352907fc980b868725387e9814822ff19035a835afeb657bb4dcb35b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c9cbb956b62333c02329180fbe03b825", "targetReference": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e"}], "guid": "bfdfe7dc352907fc980b868725387e98b78f48222917d3611c7c397d7f9ae63e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9890f568fc9b811cd56b08e401eec0f35e", "name": "rive_common-rive_common_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9849c5bdbe31e3b466d37bab9271baf60a", "name": "rive_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9884d4dff26bf6b3e66f738069c7476896", "name": "rive_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}