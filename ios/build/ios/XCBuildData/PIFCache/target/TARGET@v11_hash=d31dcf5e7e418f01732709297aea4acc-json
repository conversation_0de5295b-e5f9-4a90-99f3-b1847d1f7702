{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98850e0f0e21c4669e9147e0ef0169cfd9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849d06d87cbe8d5b7ccea799ad05b3979", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e40ed819ef80edfe93baac1e4cde999e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986da7685038a3e40e6ef0b4241e5f8c0d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e40ed819ef80edfe93baac1e4cde999e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3a427646e3c5b725bdcad9954f053be", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6365572c3591f3a173f3cc674020a65", "guid": "bfdfe7dc352907fc980b868725387e980fd7787f4ad3db1d0c3b95d101deba8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98089d1b381e5e14ed046e95ee3f6de28e", "guid": "bfdfe7dc352907fc980b868725387e98ac6805dbad5bb1ab68161ed8c28f0105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef8b708dd71ab90c0ba2fba21ae2dddc", "guid": "bfdfe7dc352907fc980b868725387e9829d9dee8b08f4f33421c82a15f28b66b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7be64513ae40a86303cf70aa03849e1", "guid": "bfdfe7dc352907fc980b868725387e980e707d1bc62ea8b7dcf8b8a6de23122d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98456e1261c29217289cc4687b05d98869", "guid": "bfdfe7dc352907fc980b868725387e981f3f0fc1192423d63a840d14a2d77a13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98841a00488617b0a1fc1ef144ade334e2", "guid": "bfdfe7dc352907fc980b868725387e98d716f628ebba2e02b8a6df280b82dc9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f242bd0360dfbc8ffa6e5eaec0f24a5", "guid": "bfdfe7dc352907fc980b868725387e98ecf543257ac259b942f5eb9f42a4f1d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7458149f7dcb9001df5bfe2b6e514ac", "guid": "bfdfe7dc352907fc980b868725387e98c91202ff465f3f84a40fe485a26cbd69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98383091035779d4c8c523da02d1a69a15", "guid": "bfdfe7dc352907fc980b868725387e9835ffed91e05f6dfb7ea0d2afb719cfff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f86115df7e915840ca2c0ca88914281f", "guid": "bfdfe7dc352907fc980b868725387e98ddfee4dc5a4a4532cdbb7b8aac17e571"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d21c9935a5e93ba5833ad544d358b18c", "guid": "bfdfe7dc352907fc980b868725387e98e88b5ad6b5d467bfc5f46d9253a7fce4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98725446f59fd1606b438d71d0298c3205", "guid": "bfdfe7dc352907fc980b868725387e98406f1dacbe20c511f04162c57186f650", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f183d12ecefcef8af0b893888740e2", "guid": "bfdfe7dc352907fc980b868725387e98c242751b3cebc3b560a70d08db36f075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2dbd42fc1683df6101bcf9cbc81f99", "guid": "bfdfe7dc352907fc980b868725387e981e35f5df1fcf214d136f47d0e4422d60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829ce43cbe4c472f38dc5993a9876ce6f", "guid": "bfdfe7dc352907fc980b868725387e9897483658de102c0f2524cf0dbe4fd0ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e980208fc280194ae267963d93b0da46", "guid": "bfdfe7dc352907fc980b868725387e98adb2ed0861cba7e10c43b64415c4b5f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4b9b352ada71701a00764988624eb25", "guid": "bfdfe7dc352907fc980b868725387e989b9d467b4b60ca0c8da7aa77f4f3b368"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adfe154c77d07b1696c19e3305677719", "guid": "bfdfe7dc352907fc980b868725387e989e4503cc994abc9a7efc6fc5f5308a1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024622d399d5cf9fac6aa03e5b93b854", "guid": "bfdfe7dc352907fc980b868725387e9880d769e420c80e661bb5da2fa29f8c36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e96a79c1a409140157f47420172176", "guid": "bfdfe7dc352907fc980b868725387e98f92a51d7a6a1c23cf2869c209a524d97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6cd31251767781fb95b65c3646ddbf8", "guid": "bfdfe7dc352907fc980b868725387e988ec9e67feca369c522ba6d65a6fa6738"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3356e306c68e20dfba079720cb07b22", "guid": "bfdfe7dc352907fc980b868725387e9886c08ab20cb47a53723fd42c908103bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d099d769b593040c97f9c85100e728", "guid": "bfdfe7dc352907fc980b868725387e9813d2b8a0a1d1c13b8855223acd798ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b7d70e4639e9d0b99e0668e6ca8e874", "guid": "bfdfe7dc352907fc980b868725387e98fc9eac5d82b6f98175be7babfb25268d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839fb12799c87ee339608a64a392f5fd7", "guid": "bfdfe7dc352907fc980b868725387e98d90b0001b422e96e30b66d39cdbe11e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a37ce88d49c23926f5497e0d6da6e4e7", "guid": "bfdfe7dc352907fc980b868725387e98057713d6e678e7f3b109c9887b333c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982762fdcae3a5911a95f7801d0f87a063", "guid": "bfdfe7dc352907fc980b868725387e98b6d67d363c4b1dea6761fb7a1454e720"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13f81b5f283983e6b0b3aef9afc069c", "guid": "bfdfe7dc352907fc980b868725387e9807dfeee07c64bd39073381d5a2f0ba21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864cc6e5b008054bdec250e04f402e56e", "guid": "bfdfe7dc352907fc980b868725387e983e5876fdbfd148b2c42d7b07b29dd1b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c31069f1069d2583b9dc4f07c11192", "guid": "bfdfe7dc352907fc980b868725387e986ecd69fa177faeb3c831fd3e7d0461df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc0316c1b7f43121738a9f4f991114c6", "guid": "bfdfe7dc352907fc980b868725387e98313a86b18c573cb61b90f3bcac8c4b56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a08b9b6009c7c515f18bb76b8c4080e1", "guid": "bfdfe7dc352907fc980b868725387e98908ffe2628a56e7d230ef822c737875d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f1aaa809a63faca15e36d8baf9aceb", "guid": "bfdfe7dc352907fc980b868725387e98049559e2287c39c8d39a9de99ceceb68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98996186493d46101aaf8045f99171ac99", "guid": "bfdfe7dc352907fc980b868725387e98264e08f98efc698dc3350317fa512985"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcc0206434e3debca15f3dce9f844240", "guid": "bfdfe7dc352907fc980b868725387e98a01ba58fd2912db7fb6422d70742236e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f08f0d2adb9c05f3cadbdf180fe75d51", "guid": "bfdfe7dc352907fc980b868725387e98a1ab2bf7750e57872287a02588452ca1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a8890b76e15e7db59738f75545970d1", "guid": "bfdfe7dc352907fc980b868725387e981f1021f5eaca6da309faf9be9cda6d8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98892a308c458f3c5e59944579199fc759", "guid": "bfdfe7dc352907fc980b868725387e98866e783f01f06cf05ea5f6bac0dfc2c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880155b05e20bbb97b9f9016071ba3581", "guid": "bfdfe7dc352907fc980b868725387e988567f0fffaed2ee5b657eb7bd822e119"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866b556345b40b903e01f068eb84ee0b2", "guid": "bfdfe7dc352907fc980b868725387e98e85b858dd4433349fe256750471bedb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c78c1e07a9c45e1108b9939f4e6521c", "guid": "bfdfe7dc352907fc980b868725387e98437e4e42ca385bea3e47351aa18e61de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d16540ac1c1d0200ab24e67db390cd", "guid": "bfdfe7dc352907fc980b868725387e9870615ecef7519cb61a52542e716e9ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98402378aee33b0653b895a2994f2db477", "guid": "bfdfe7dc352907fc980b868725387e983678479f900018368ca324a307aa52bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e2b1b737688ef0b7e8bc6f945555b7f", "guid": "bfdfe7dc352907fc980b868725387e98afc92a218dcc9c9d3ff1682bf888b403"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838bef0c4076e7588742c3bf0c96d9a90", "guid": "bfdfe7dc352907fc980b868725387e982b2c73317418116e683553b19b2fe2f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2b850845a47c86f9fedf9feeba8d017", "guid": "bfdfe7dc352907fc980b868725387e9809c8ee03b0c4673f81cb779170ba490f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8cda3937d28a5f8c9eba9350c88dfe", "guid": "bfdfe7dc352907fc980b868725387e98d782d39d832fdad495421da85351b885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98467dd766fdfb757e0866c50ee29256d2", "guid": "bfdfe7dc352907fc980b868725387e988299ef62e7f1105d4d4aad3251a4d9f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deffb732b89c80c0d1f662bb3547e110", "guid": "bfdfe7dc352907fc980b868725387e98118d1fb815b2bb914b101b1f6f22af15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856c7b9e3b9bda9872471498791fb1de7", "guid": "bfdfe7dc352907fc980b868725387e988ae5b2414ef7331ad866c59304e1dfd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f26ee11ced986434b49e320c7b54af5d", "guid": "bfdfe7dc352907fc980b868725387e9886603e0cfc21016fab7caa822098600d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32146f09199a9f7bd661be2f5f91c50", "guid": "bfdfe7dc352907fc980b868725387e98a0c452db553cade616c431fc5d4f92cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416cdb5dc647ae11f6626c62038ca5ac", "guid": "bfdfe7dc352907fc980b868725387e9827f011b9139460b852e0900c9ae01bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1f0692edc97ba49aee121b5f218fe56", "guid": "bfdfe7dc352907fc980b868725387e989283e1ba923051957bb308a355c814d1"}], "guid": "bfdfe7dc352907fc980b868725387e98a7cead267596a564fea5270336416be7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d43391200432735a9a3b9cf72756744", "guid": "bfdfe7dc352907fc980b868725387e980993d05392b4579d63012053e95b28d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c40c9719623a1ce0e8d8567a87d8d6d6", "guid": "bfdfe7dc352907fc980b868725387e98c170f90e58b34fc16b2a63bdbaa1e81e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e452a04e7030d68d43702599ee13d4e2", "guid": "bfdfe7dc352907fc980b868725387e9861e3f91a7fdcf1726d1b6bd25275c25b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d34e36721eb1a611cc202262d366ae11", "guid": "bfdfe7dc352907fc980b868725387e98269515d7fef4dd13da6cc9abeebaf95c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d884210177b5a2ef3c5c663257ac1287", "guid": "bfdfe7dc352907fc980b868725387e98dfb9e38233c3caaf676c0bf4094d3f52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98224a6014593573953b3072ec10568f1d", "guid": "bfdfe7dc352907fc980b868725387e986886c77f142fe85a8f333aa79c70a065"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8797bedfa67697f06a15b2e6f834a1", "guid": "bfdfe7dc352907fc980b868725387e982ab42748549e5a7a96277687e2b42362"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa5a76ddf0c22b2d3286ceb7852de3c", "guid": "bfdfe7dc352907fc980b868725387e988f8504cdc05d0cbd5c0ad88fa121f0a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a667c3722b1ab7fa34043f47075d42bc", "guid": "bfdfe7dc352907fc980b868725387e988de9994a3a03d00a5a0994d275daf741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd1bbc645a404482adb2e745268c4094", "guid": "bfdfe7dc352907fc980b868725387e98da9b6b4f8b1a784566ad3ba765388b4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ade033f6c5207dc50935aef5d8cef3b", "guid": "bfdfe7dc352907fc980b868725387e98201c49112b7efa1bd0cfc13f833ecc12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7eed1f5e7145aafaf5f41b6d6e7dc88", "guid": "bfdfe7dc352907fc980b868725387e9815fd0eaa927c19be99e0c4903a109217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78b468f15a6b50eb48ed6f7da2b0057", "guid": "bfdfe7dc352907fc980b868725387e986ad9cbdc6c5270f9c3355d27ee61a906"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c87bdfb09064af6c656b32ceb4982a", "guid": "bfdfe7dc352907fc980b868725387e98f46ff47d846cb56bb6e9b793ddcce3bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830d5fdcda57368f62b77e47e484949f4", "guid": "bfdfe7dc352907fc980b868725387e98b6e6347e5cf597f5a44d9d5b2e711dea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6b54267e6bd60bee036e076b77c725", "guid": "bfdfe7dc352907fc980b868725387e988222c3d25bd47905f0bbe37ff4a538d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5fbe39b9ad32a8204484c665cae8ee5", "guid": "bfdfe7dc352907fc980b868725387e9893541e2c910a00aa39ca50c95ddbc779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ef255f77e7cb2926de7e5d9c550b621", "guid": "bfdfe7dc352907fc980b868725387e98fe61ee346bd5580091229795d159171c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f73ff74ce395fad2b166fd18cd1e2e9", "guid": "bfdfe7dc352907fc980b868725387e987d14f211f06015aba306930b7aaaf18a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881a208d6d8064d1b276cdab715e41fce", "guid": "bfdfe7dc352907fc980b868725387e98027c49ecd6f4c8698f53d49e41d5d244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806bb62ca8309f786e025f84815630d90", "guid": "bfdfe7dc352907fc980b868725387e9871a1c1253b4adfe0223d61bb3e45a6a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8616353b5a6a2e16334e7088ce18493", "guid": "bfdfe7dc352907fc980b868725387e987b1c272a63e5a75bc5f845912984e67a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a9991834399fdb34dbd5040f21b8f3", "guid": "bfdfe7dc352907fc980b868725387e98126147a1b5a29c4536cdde5a1cbdf747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9ff2b198ac9ba36c8977114ae10677", "guid": "bfdfe7dc352907fc980b868725387e98ed10dca9cb245feab43098fb26d92c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4da37dbb49933a3aa56d03f1ebcc1c8", "guid": "bfdfe7dc352907fc980b868725387e982cb233ea1439641c56b3d028eb7f4790"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acbd472191006745e5065d167f0ee314", "guid": "bfdfe7dc352907fc980b868725387e989de6cb468e64dfffaade5d79302237a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5fc4c500381c47d2c571566943579ae", "guid": "bfdfe7dc352907fc980b868725387e98da3d07112c72ee4325c28e6c9537e254"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ba7c313dbf1c8b57fe600e17ba7257c", "guid": "bfdfe7dc352907fc980b868725387e98240f710d11092c17bf57e040f45dd28e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9857acb9ee9687cd5eebc89e12cf523", "guid": "bfdfe7dc352907fc980b868725387e98030c45593a530e4b54f90ea8a9132756"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851a7046393a1dea88200d4b1a0eb2b90", "guid": "bfdfe7dc352907fc980b868725387e98697277ce4683e7d182c34055e47b1b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad14d7c5cf3eea4c6b66090b372b6065", "guid": "bfdfe7dc352907fc980b868725387e989665d46ecfc581e010fbf3f9ab627079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981aa362bb5d763eca7a81f2206ba23eb0", "guid": "bfdfe7dc352907fc980b868725387e98fd7b8f3c90e4ab49a4976e6592d9a7e7"}], "guid": "bfdfe7dc352907fc980b868725387e9842b8f58d4630ac41fc37b4e11c8f6ff6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98f6ed4d0e47c5b2dda1f387e927d9a92f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e986cef4edb467032612ee07acccbfeea93"}], "guid": "bfdfe7dc352907fc980b868725387e98a680a8ff99f43d41a05129a3d1980e25", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981e1e968b2d48142b63497f84918ea363", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e98b3386cc3c3b0bf0214e1dad10e3a315e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}