{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bc046178b05ce262fa1277e49d4c075e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c4db054f21a32a7169d5861425da0b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c4db054f21a32a7169d5861425da0b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/opt/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988765a794e1d5119295f6657cd0311f4a", "guid": "bfdfe7dc352907fc980b868725387e98a2db98872b555d8fbf93817119e836e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3b036776223dbce141e3a635cb01af4", "guid": "bfdfe7dc352907fc980b868725387e981dafb7508e8ec9eda876a96c4fd55084", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c518d6040d0e9d159b771a707982ac7", "guid": "bfdfe7dc352907fc980b868725387e98f2230812ed122b9941b5c1c2bc23b359", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817bb5de466e21a708e80cd514253be36", "guid": "bfdfe7dc352907fc980b868725387e98a87b71f55e669fd7f2bfea9f575bd0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f0c5d9411c76e742a8d93f4912eae7", "guid": "bfdfe7dc352907fc980b868725387e9881945b9667c5fef059b602d14ea44ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bdf287e021a165c278ff4ccfa5d41f9", "guid": "bfdfe7dc352907fc980b868725387e98db24817f29284d485f292c4f921311ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a400aefc785465dbaef89f45bab160ab", "guid": "bfdfe7dc352907fc980b868725387e98b1882bdaadbb14fcf3f3b022702e8caa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874425cebb38216a45280b94359cb5c36", "guid": "bfdfe7dc352907fc980b868725387e984a394731400b57c6cb93b4c5ff0602cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fbbb08b159c331250fe17f824003a90", "guid": "bfdfe7dc352907fc980b868725387e985d90cb3054c890fa0c873a1b463100d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d91c8c74670caadf7bd29e45c71fe0d", "guid": "bfdfe7dc352907fc980b868725387e9829ce3e7d24e9f1ab89026344216c1b95", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0741e1316f69d258d130fd3de362be9", "guid": "bfdfe7dc352907fc980b868725387e986ee4679b91091a0c6015b0f772d0d3a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed40bdce5ce1edb4c2dd3eceb67d41ba", "guid": "bfdfe7dc352907fc980b868725387e98d8aa906e97e11fc427c0bf94b1ecad31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a55795293ecbb9901d972b057ad10b", "guid": "bfdfe7dc352907fc980b868725387e98c07d025e8068c5efce4c04d5a01a288e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f488cb2f1119bf9e998342a1ac977214", "guid": "bfdfe7dc352907fc980b868725387e98e43758b548da6d9fb0bbdd3e222e831f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c86efb37dfcc04ddd831d86a7bca6491", "guid": "bfdfe7dc352907fc980b868725387e981caebedf975e61a183ebf85c58106f40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986184bd2b3da6ee0ff0e5907f495fadef", "guid": "bfdfe7dc352907fc980b868725387e987e7b131de44c9017d66a30a5dc22f345", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989630dffdd19935786d4a2b8fc75c0d17", "guid": "bfdfe7dc352907fc980b868725387e98f40c178074dcd920805bde1241e04cd1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fa95c766c85acb8abb41532e55bf26e3", "guid": "bfdfe7dc352907fc980b868725387e98a542bbecff2b65c55a8945f293c83c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed2ec85ba841fb8a4ce2bc685d2b2aed", "guid": "bfdfe7dc352907fc980b868725387e983bec3c1aad328bc92a90e3efa6acb2bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e4bd718ef72778ccfa1607a37ddd6ce", "guid": "bfdfe7dc352907fc980b868725387e98a94ce6269fac4850aed5eea72481bbd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985101ccff3ce389c32ae01900d0ce53c3", "guid": "bfdfe7dc352907fc980b868725387e988bbbe1ec1bfb24b30a482b0b68cad47d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983be00529d77256b73a818e46cb1270ca", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dfba3ef227c90cb0c6699c5d72b47fd", "guid": "bfdfe7dc352907fc980b868725387e988de3ea169b8740bd2c08f97ab38ed551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdefd54ffc4934887f46743e86d57261", "guid": "bfdfe7dc352907fc980b868725387e98fd54b0d65208c8f1527baab6fb5c8241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c5c86ffd5765ce6b3e88f0ae5b74f1c", "guid": "bfdfe7dc352907fc980b868725387e98eb09de490dfb5df693bdb058a0797c2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f91f1e55e1938ea3e2caf9c58aea58e", "guid": "bfdfe7dc352907fc980b868725387e98ed41193c064c816ee6d5756fcbb80f38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879eb77ceb2f0a50ed34debc974c5779d", "guid": "bfdfe7dc352907fc980b868725387e981e3511580c3b1ed99a16abd7c66a0b79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d949f7a4ef3567431402fbcd0438ab48", "guid": "bfdfe7dc352907fc980b868725387e98e94e4133164a6cfaebdf4740a9eeebc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dec44983f184719789873e5bd934342b", "guid": "bfdfe7dc352907fc980b868725387e98c097fac8a65293040f9826ee377dc4f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d8e25da3d4c99eeea4108b74f0806f7", "guid": "bfdfe7dc352907fc980b868725387e982873b45d930a8bda719b3b055b9db278"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee6a2223f37ece54ddb7e335db5e869", "guid": "bfdfe7dc352907fc980b868725387e98ffc4173a717cb8c87d243362e01e4b48"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}