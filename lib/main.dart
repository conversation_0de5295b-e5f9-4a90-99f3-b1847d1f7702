// lib/main.dart
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'Constant/routes.dart';
import 'Constant/theme.dart';
import 'models/user.dart';
import 'services/connectivity_wrapper.dart';
import 'services/my_navigator_observer.dart';
import 'screens/Startup/loading_branding_screen.dart';

// Background message handler (must be a top-level function)
// It's crucial to initialize Firebase here again for a separate instance.
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print("✅ Handling a background message: ${message.messageId}");
}

// A global key for the navigator, allowing navigation from anywhere.
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first.
  await Firebase.initializeApp();

  // Register the background message handler.
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // Initialize Hive and register adapters.
  await Hive.initFlutter();
  Hive.registerAdapter(UserAdapter());
  // Register other adapters here...

  // Open Hive boxes.
  await Hive.openBox('box');
  await Hive.openBox('boxGlobal');
  await Hive.openBox('boxColor');
  await Hive.openBox('boxMenu');

  // Configure Flutter EasyLoading.
  EasyLoading.instance
    ..dismissOnTap = false
    ..userInteractions = false
    ..contentPadding = EdgeInsets.zero
    ..radius = 80
    ..maskType = EasyLoadingMaskType.black;

  // Configure System UI Overlay.
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark.copyWith(
    statusBarColor: Colors.transparent, // Android
    statusBarBrightness: Brightness.light, // iOS
  ));

  // Get package info.
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  // String versionNumber = packageInfo.version;

  // Run the app.
  runApp(const ProviderScope(child: MainApp()));
}

class MainApp extends StatelessWidget {
  const MainApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ConnectivityWrapper(
      navigatorKey: navigatorKey,
      child: MaterialApp(
        title: 'Blue Track Analytics',
        navigatorObservers: [MyNavigatorObserver()],
        theme: theme(context),
        home: const LoadingBrandScreen(),
        routes: routes,
        navigatorKey: navigatorKey,
        builder: EasyLoading.init(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}