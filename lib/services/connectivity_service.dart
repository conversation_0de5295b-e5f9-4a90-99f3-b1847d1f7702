import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();
  ConnectivityResult _previousResult = ConnectivityResult.none;
  final Function onConnectivityChanged;

  ConnectivityService(this.onConnectivityChanged) {
    _initializeConnectivity();
    _connectivity.onConnectivityChanged.listen(_checkStatus);
  }

  Future<void> _initializeConnectivity() async {
    _previousResult = await _connectivity.checkConnectivity();
  }

  void _checkStatus(ConnectivityResult result) {
    if (result == ConnectivityResult.none) {
      onConnectivityChanged(false);
    } else {
      if (_previousResult == ConnectivityResult.none) {
        onConnectivityChanged(true);
      }
    }
    _previousResult = result;
  }
}
