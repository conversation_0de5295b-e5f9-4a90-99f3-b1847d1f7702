import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alert.dart';
import 'package:hive/hive.dart';
import 'package:maeko/services/network_service.dart';
import 'api.dart' as api;
import 'api_post.dart';

class ApiGet {
  final NetworkService networkService = NetworkService();

 Future<Map?> alerts(
  String filter,
  int nextPage, {
  String? keyword,
  int? durationValue,
  String? durationUnit,
}) async {
  try {
    // Build query parameters dynamically
    final queryParameters = <String, String>{
      'filter': filter,
      if (nextPage > 0) 'page': nextPage.toString(),
      if (keyword != null && keyword.trim().isNotEmpty) 'keyword': keyword.trim(),
      if (durationValue != null) 'duration_value': durationValue.toString(),
      if (durationUnit != null && durationUnit.trim().isNotEmpty) 'duration_unit': durationUnit.trim(),
    };

    Uri uri = Uri.https(
      api.domain,
      api.projectName + api.alertList,
      queryParameters,
    );

    Box box = Hive.box('box');
    final String? accessToken = box.get("access_token");

    if (accessToken == null || accessToken.isEmpty) {
      throw Exception("No access token found.");
    }

    Response response = await networkService.get(uri, headers: {
      HttpHeaders.authorizationHeader: 'Bearer $accessToken',
      HttpHeaders.contentTypeHeader: 'application/json',
    });

    print("Request URL: $uri");
    print("Response Code: ${response.statusCode}");
    print("Response Body: ${response.body}");

    if (response.statusCode != 200) {
      // Handle common HTTP errors
      if (response.statusCode == 401 || response.statusCode == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          // Prevent infinite loop if refresh still fails
          return alerts(filter, nextPage,
              keyword: keyword,
              durationValue: durationValue,
              durationUnit: durationUnit);
        }
      }
      throw Exception("Failed to fetch alerts: ${response.statusCode}");
    }

    Map<String, dynamic> data = jsonDecode(response.body);

    return data;
  } catch (e, stack) {
    print("Error in alerts(GET): $e");
    print(stack);
    return null;
  }
}

Future<Map?> me() async {
    // try {
    Uri uri = Uri.https(api.domain, api.projectName + api.me);

    Box box = Hive.box('box');
    final String accessToken = box.get("access_token");

    Response response = await networkService.get(uri, headers: {
      HttpHeaders.authorizationHeader: 'Bearer $accessToken',
      HttpHeaders.contentTypeHeader: 'application/json',
    });

    Map data = jsonDecode(response.body);
    print(response.body);
    if (data['code'] == 403) {
      int? refreshCode = await ApiPost().accessTokenRefresh();
      if (refreshCode == 200) {
        return me();
      }
    }
    return data;
    // } catch (e) {
    //   print("Errors found in me(GET): $e");
    //   return null;
    // }
  }


  Future<Map?> alertDetail(int id) async {
    try {
      Uri uri = Uri.https(
          api.domain, api.projectName + api.alertDetail + id.toString());

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token");

      Response response = await networkService.get(uri, headers: {
        HttpHeaders.authorizationHeader: 'Bearer $accessToken',
        HttpHeaders.contentTypeHeader: 'application/json',
      });


      Map data = jsonDecode(response.body);
      print("Alert Detail Response: ${response.body}");
      return data;
    } catch (e) {
      print("Errors found in alertDetail(GET): $e");
      return null;
    }
  }

}