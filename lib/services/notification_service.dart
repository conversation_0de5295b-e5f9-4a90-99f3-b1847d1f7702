import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:maeko/services/api_post.dart';
import 'package:hive/hive.dart';
import 'package:maeko/screens/home_page.dart';
import 'package:flutter/material.dart'; 
import 'package:maeko/main.dart';

class NotificationService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static String? _fcmToken;
  
  // Constants for Hive keys
  static const String _boxName = "box";
  static const String _fcmTokenKey = "fcm_token";
  static const String _pushNotificationKey = "pushNotification";
  static const String _firstLaunchKey = "isFirstLaunch";
  
  // Getter for FCM token (replaces FirebaseService.fcmToken)
  static String? get fcmToken => _fcmToken;
  
  // Setter for FCM token
  static void setFcmToken(String? token) {
    _fcmToken = token;
    print("FCM Token set: $token");
  }

  // Check if this is the first time launching the app
  static Future<bool> isFirstLaunch() async {
    try {
      Box box = await Hive.openBox(_boxName);
      return box.get(_firstLaunchKey, defaultValue: true);
    } catch (e) {
      print("Error checking first launch: $e");
      return true;
    }
  }
  
  // Mark that the app has been launched before
  static Future<void> setFirstLaunchComplete() async {
    try {
      Box box = await Hive.openBox(_boxName);
      await box.put(_firstLaunchKey, false);
      print("First launch marked as complete");
    } catch (e) {
      print("Error setting first launch complete: $e");
    }
  }
  
  // Get current push notification setting
  static Future<bool> getPushNotificationSetting() async {
    try {
      Box box = await Hive.openBox(_boxName);
      return box.get(_pushNotificationKey, defaultValue: true);
    } catch (e) {
      print("Error getting push notification setting: $e");
      return true; // Default to enabled
    }
  }
  
  // Save push notification setting
  static Future<void> setPushNotificationSetting(bool enabled) async {
    try {
      Box box = await Hive.openBox(_boxName);
      await box.put(_pushNotificationKey, enabled);
      print('Push notification setting saved: $enabled');
      
      // If disabling notifications, remove token from backend
      if (!enabled) {
        NotificationService notificationService = NotificationService();
        String? newToken = await _firebaseMessaging.getToken();
        print("🔕 Disabling notifications - removing token: $newToken");
        await notificationService.removeTokenFromBackend(newToken!);
      } else {
        // If enabling notifications, send token to backend
        NotificationService notificationService = NotificationService();
        await notificationService.saveNotificationToken();
      }
    } catch (e) {
      print("Error saving push notification setting: $e");
    }
  }
  
  // Setup default settings on first launch
  static Future<void> setupDefaultSettings() async {
    try {
      bool firstLaunch = await isFirstLaunch();
      
      if (firstLaunch) {
        // Set default push notification to enabled
        Box box = await Hive.openBox(_boxName);
        await box.put(_pushNotificationKey, true);
        
        // Mark first launch as complete
        await setFirstLaunchComplete();
        
        print('✅ First launch setup complete - Push notifications enabled by default');
      } else {
        print('📱 Not first launch - loading existing settings');
      }
    } catch (e) {
      print("Error setting up default settings: $e");
    }
  }

  // Your original initialise method + FirebaseService initialize functionality
  initialise() async {
    try {
      // Setup default settings if first launch
      await setupDefaultSettings();
      
      // Check if push notifications are enabled in settings
      bool pushNotificationsEnabled = await getPushNotificationSetting();
      
      if (!pushNotificationsEnabled) {
        print('🔕 Push notifications disabled in settings - skipping initialization');
        return;
      }

      // Request notification permission (from FirebaseService)
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized ||
          settings.authorizationStatus == AuthorizationStatus.provisional) {
        print('✅ Notification permission granted');

        // iOS only — wait for APNs token to be set (from FirebaseService)
        if (Platform.isIOS) {
          String? apnsToken = await _firebaseMessaging.getAPNSToken();
          print("🍎 APNs Token: $apnsToken");
        }

        // Get FCM token and store it
        String? token = await _firebaseMessaging.getToken();
        print("🔑 FCM Token: $token");
        _fcmToken = token;
        
        // Save to local storage
        if (token != null) {
          await _saveTokenLocally(token);
        }

        // Set foreground notification options (your original code)
        await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: true,
        );

        // Handle initial message (your original + FirebaseService)
        FirebaseMessaging.instance.getInitialMessage().then((message) {
          if (message != null) {
            print("📱 App opened from terminated by notification: ${message.data}");
          } else {
            print("getInitialMessage - no message");
          }
        });

        // Foreground message handler (from both services)
        FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
          // Check if notifications are still enabled before showing
          bool notificationsEnabled = await getPushNotificationSetting();
          if (!notificationsEnabled) {
            print('🔕 Notifications disabled - ignoring foreground message');
            return;
          }
          
          print('📩 Foreground message received!');
          print('📦 Data: ${message.data}');
          print(message.toMap()); // Your original logging
          
          // Check if the current route is the HomePage
          final currentContext = navigatorKey.currentContext;
          final currentRoute = ModalRoute.of(currentContext!)?.settings.name;
          if (currentRoute == HomePage.routeName) {
            // Change the index to the Alerts tab
            HomePage.homeKey.currentState?.setIndex(1);
          }

          if (message.notification != null) {
            _showNotification(message.notification!.title ?? 'Notification');
          }
        });

        FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
          final currentContext = navigatorKey.currentContext;

          if (currentContext == null) return;

          // Check if we are already on the home page
          final currentRoute = ModalRoute.of(currentContext)?.settings.name;
          if (currentRoute == HomePage.routeName) {
            // If on the home page, change the index to the Alerts tab
            HomePage.homeKey.currentState?.setIndex(1);
          } else {
            // If on another route, navigate to the home page first and then change index
            navigatorKey.currentState?.pushNamedAndRemoveUntil(HomePage.routeName, (route) => route.isFirst);
            HomePage.homeKey.currentState?.setIndex(1);
          }
        });

        // Handle token refresh (from FirebaseService)
        FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
          print("♻️ Token refreshed: $newToken");
          _fcmToken = newToken;
          await _saveTokenLocally(newToken);
          
          // Only send to backend if notifications are enabled
          bool notificationsEnabled = await getPushNotificationSetting();
          if (notificationsEnabled) {
            await _sendTokenToBackend(newToken);
          }
        });
        
      } else {
        print('❌ User declined or has not accepted notification permission');
        // Update settings to reflect that notifications are disabled
        await setPushNotificationSetting(false);
      }
    } catch (e) {
      print("Error in notification service initialization: $e");
    }
  }

  // Your original saveNotificationToken method (enhanced)
  saveNotificationToken() async {
    try {
      // Check if notifications are enabled in settings
      bool pushNotificationsEnabled = await getPushNotificationSetting();
      if (!pushNotificationsEnabled) {
        print('🔕 Push notifications disabled in settings - not saving token');
        return;
      }

      // If we don't have a token yet, try to get one
      if (_fcmToken == null) {
        _fcmToken = await _firebaseMessaging.getToken();
      }
      
      String? token = _fcmToken;
      
      if (token != null) {
        await _saveTokenLocally(token);
        await _sendTokenToBackend(token);
      } else {
        print("No FCM token available");
      }
    } catch (e) {
      print("Errors found in saveNotificationToken: $e");
    }
  }
  
  // Save token to Hive (from enhanced FirebaseService)
  Future<void> _saveTokenLocally(String token) async {
    try {
      Box box = await Hive.openBox(_boxName);
      await box.put(_fcmTokenKey, token);
      print("📱 FCM token saved locally");
    } catch (e) {
      print("Error saving FCM token locally: $e");
    }
  }
  
  // Load token from Hive (from enhanced FirebaseService)
  static Future<void> loadStoredToken() async {
    try {
      Box box = await Hive.openBox(_boxName);
      _fcmToken = box.get(_fcmTokenKey);
      print("📱 Loaded stored FCM token: $_fcmToken");
    } catch (e) {
      print("Error loading stored FCM token: $e");
    }
  }
  
  // Send token to backend (from enhanced FirebaseService)
  Future<void> _sendTokenToBackend(String token) async {
    try {
      ApiPost apiPost = ApiPost();
      Map? data = await apiPost.updateToken(token);
      print("🚀 FCM token sent to backend: $data");
    } catch (e) {
      print("Error sending FCM token to backend: $e");
    }
  }
  
  // Method to manually refresh and update token (from FirebaseService)
  static Future<String?> refreshToken() async {
    try {
      await _firebaseMessaging.deleteToken();
      String? newToken = await _firebaseMessaging.getToken();
      _fcmToken = newToken;
      if (newToken != null) {
        NotificationService notificationService = NotificationService();
        await notificationService._saveTokenLocally(newToken);
        
        // Only send to backend if notifications are enabled
        bool notificationsEnabled = await getPushNotificationSetting();
        if (notificationsEnabled) {
          await notificationService._sendTokenToBackend(newToken);
        }
      }
      return newToken;
    } catch (e) {
      print("Error refreshing FCM token: $e");
      return null;
    }
  }

  // Show notification method (from FirebaseService)
  static void _showNotification(String message) {
   final currentContext = navigatorKey.currentContext;

    if (currentContext == null) return;

    final currentRoute = ModalRoute.of(currentContext)?.settings.name;
    
    // Always go to listing only - don't navigate to detail
    if (currentRoute == '/alert') {
      // Already at listing -> do nothing
      return;
    }

    // Navigate to listing page
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      '/alert',
      (route) => route.isFirst,
    );
  }

  // Method to remove token from backend
  Future<Map?> removeTokenFromBackend(String token) async {
    try {
      ApiPost apiPost = ApiPost();
      Map? data = await apiPost.removeToken(token);
      print("🗑️ FCM token removed from backend: $data");
      return data;
    } catch (e) {
      print("Error removing FCM token from backend: $e");
      return null;
    }
  }
}