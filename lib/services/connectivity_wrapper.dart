import 'package:flutter/material.dart';
import 'package:maeko/services/my_navigator_observer.dart';
import 'connectivity_service.dart';
import 'package:maeko/screens/Custom%20Widgets/no_internet_dialog.dart';

class ConnectivityWrapper extends StatefulWidget {
  final Widget child;
  final GlobalKey<NavigatorState> navigatorKey;

  const ConnectivityWrapper(
      {super.key, required this.child, required this.navigatorKey});

  @override
  // ignore: library_private_types_in_public_api
  ConnectivityWrapperState createState() => ConnectivityWrapperState();
}

class ConnectivityWrapperState extends State<ConnectivityWrapper> {
  // ignore: unused_field
  late ConnectivityService _connectivityService;
  bool _isDialogVisible = false;

  @override
  void initState() {
    super.initState();
    _connectivityService = ConnectivityService(onConnectivityChanged);
  }

  void onConnectivityChanged(bool isConnected) {
    if (!isConnected) {
      showNoInternetDialog();
    } else {
      if (_isDialogVisible) {
        Navigator.of(widget.navigatorKey.currentContext!).pop();
        _isDialogVisible = false;
        _refreshCurrentPage();
      }
    }
  }

  void showNoInternetDialog() {
    if (!_isDialogVisible) {
      _isDialogVisible = true;
      showDialog(
        context: widget.navigatorKey.currentContext!,
        builder: (context) {
          return const NoInternetDialog();
        },
      ).then((_) {
        _isDialogVisible = false;
      });
    }
  }

  void _refreshCurrentPage() {
    String? routeName = routeStacks.last?.settings.name;
    Navigator.of(widget.navigatorKey.currentContext!)
        .pushReplacementNamed(routeName!);
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
