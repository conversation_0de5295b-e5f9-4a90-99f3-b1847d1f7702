import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';

class FirebaseService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  static String? _fcmToken;

  static String? get fcmToken => _fcmToken;

  static Future<void> initialize() async {
    // Request notification permission
    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized ||
        settings.authorizationStatus == AuthorizationStatus.provisional) {
      print('✅ Notification permission granted');

      // iOS only — wait for APNs token to be set
      if (Platform.isIOS) {
        String? apnsToken = await _firebaseMessaging.getAPNSToken();
        print("🍎 APNs Token: $apnsToken");
      }

      // Get FCM token
      String? token = await _firebaseMessaging.getToken();
      print("🔑 FCM Token: $token");
      _fcmToken = token;

      // Foreground message handler
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        print('📩 Foreground message received!');
        print('📦 Data: ${message.data}');
        if (message.notification != null) {
          print('🔔 Notification: ${message.notification!.title} - ${message.notification!.body}');
          _showNotification(message.notification!.title ?? 'Notification');
        }
      });

      // App opened from terminated via notification
      FirebaseMessaging.instance.getInitialMessage().then((message) {
        if (message != null) {
          print("📱 App opened from terminated by notification: ${message.data}");
        }
      });

      // App opened from background via notification
      FirebaseMessaging.onMessageOpenedApp.listen((message) {
        print("🚪 App opened from background by notification: ${message.data}");
      });

      // Handle token refresh
      FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
        print("♻️ Token refreshed: $newToken");
        _fcmToken = newToken;
        // TODO: Send to server
      });
    } else {
      print('❌ User declined or has not accepted notification permission');
    }
  }

  static void _showNotification(String message) {
    // This would typically show a local notification
    // For now, it just prints to console
    print('Notification: $message');
  }
}