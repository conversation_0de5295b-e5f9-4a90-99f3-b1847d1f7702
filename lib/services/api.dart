import 'package:maeko/Constant/important_variables.dart' as important_variables;

// test server - voker
// const String domain = "obriens-api.voker.my";
// const String projectName = "";
// const String inFront = "";

// live server - oberiens
// const String domain = "obriens-api.voker.my";
// const String projectName = "";
// const String inFront = "";

// // live server 2 - oberiens
// const String domain = "obriens-v4-api.voker.my";
// const String projectName = "";
// const String inFront = "";

//https://demo.bluetrack-ai.com/bell/api/public/

// test server 
// const String domain = "test.invoke.my";
// const String projectName = "/vision/bell_api/public";
// const String inFront = "";

// demo server 
const String domain = "demo.bluetrack-ai.com";
const String projectName = "/bell/api/public";
const String inFront = "";

const String login = inFront + "/login";
const String accessTokenRefresh = inFront + "/access-token/refresh";

// me
const String me = inFront + "/me";
const String meUpdate = inFront + "/me/update";
const String meUpdatePassword = inFront + "/me/password/update";
const String meDeleteAccount = inFront + "/me/destroy";

// points
const String alertList = inFront + "/notifications";
const String alertDetail = inFront + "/notifications/";

// token
const String tokenUpdate = inFront + "/notifications/tokens/store";
const String removeToken = inFront + "/notifications/tokens/destroy";

// // auth
// const String accessTokenRefresh = inFront + "/access-token/refresh";
// const String loginWithPassword = inFront + "/login/password";
// const String loginWithOTP = inFront + "/login/otp";
// const String verifyOTPLogin = inFront + "/login/otp/verify";

// const String register = inFront + "/register";
// const String socialRegister = inFront + "/register/social";
// const String requestVerificationCode = inFront + "/phone/send";
// const String verifyPhone = inFront + "/phone/verify";
// const String requestResetPasswordCode = inFront + "/password/forgot";
// const String resetPassword = inFront + "/password/reset";
