import 'dart:convert';

import 'package:maeko/main.dart';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'connectivity_wrapper.dart';

class NetworkService {
  // final Connectivity _connectivity = Connectivity();

  Future<bool> _isConnected() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  Future<http.Response> get(var url, {Map<String, String>? headers}) async {
    if (await _isConnected()) {
      return http.get(url, headers: headers);
    } else {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      state?.showNoInternetDialog();
      throw Exception('No internet connection');
    }
  }

  Future<http.Response> post(var url,
      {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    if (await _isConnected()) {
      return http.post(url, headers: headers, body: body, encoding: encoding);
    } else {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      state?.showNoInternetDialog();
      throw Exception('No internet connection');
    }
  }

  Future<http.Response> put(var url,
      {Map<String, String>? headers, Object? body, Encoding? encoding}) async {
    if (await _isConnected()) {
      return http.put(url, headers: headers, body: body, encoding: encoding);
    } else {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      state?.showNoInternetDialog();
      throw Exception('No internet connection');
    }
  }

  Future<http.Response> delete(var url, {Map<String, String>? headers}) async {
    if (await _isConnected()) {
      return http.delete(url, headers: headers);
    } else {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      state?.showNoInternetDialog();
      throw Exception('No internet connection');
    }
  }
}
