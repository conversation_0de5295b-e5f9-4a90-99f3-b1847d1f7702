import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('isLoggedIn') ?? false;
  }

  static Future<void> saveLoginData(String token, String phoneNumber) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isLoggedIn', true);
    await prefs.setString('authToken', token);
    await prefs.setString('userPhone', phoneNumber);
  }

  static Future<String?> getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('authToken');
  }

  static Future<void> saveUserProfile({
    required String name,
    required String phone,
    required String email,
    required bool pushNotifications,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userName', name);
    await prefs.setString('userPhone', phone);
    await prefs.setString('userEmail', email);
    await prefs.setBool('pushNotifications', pushNotifications);
  }

  static Future<void> saveUser({
    required String name,
    required String phone,
    required String email,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userName', name);
    await prefs.setString('userPhone', phone);
    await prefs.setString('userEmail', email);
  }


  static Future<Map<String, dynamic>> getUserProfile() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'name': prefs.getString('userName') ?? '',
      'phone': prefs.getString('userPhone') ?? '',
      'email': prefs.getString('userEmail') ?? '',
      'pushNotifications': prefs.getBool('pushNotifications') ?? true,
    };
  }

  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }
}
