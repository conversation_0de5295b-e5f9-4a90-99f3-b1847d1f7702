import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alert.dart';
import 'package:hive/hive.dart';
import 'package:maeko/services/network_service.dart';
import 'api.dart' as api;

class ApiPost {
  final NetworkService networkService = NetworkService();

   Future<int?> accessTokenRefresh() async {
    Box box = await Hive.openBox('box');
    String? refreshToken = box.get("refresh_token");

    if (refreshToken != null) {
      try {
        var queryParameters = {'refresh_token': refreshToken};

        var uri = Uri.https(api.domain,
            api.projectName + api.accessTokenRefresh, queryParameters);
        Response response = await networkService.post(uri, headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
        });

        Map data = jsonDecode(response.body);
        if (data['code'] == 200) {
          await box.put("access_token", data['data']['access_token']);
          await box.put("refresh_token", data['data']['refresh_token']);
          await box.put("is_login", true);
        } else {
          await box.delete("access_token");
          await box.delete("refresh_token");
          await box.put("is_login", false);
        }
        return data['code'];
      } catch (e) {
        print("Errors found in accessTokenRefresh(POST): $e");
        await box.delete("access_token");
        await box.delete("refresh_token");
        await box.put("is_login", false);
        return null;
      }
    } else {
      await box.delete("access_token");
      await box.delete("refresh_token");
      await box.put("is_login", false);
      return null;
    }
  }

  Future<Map?> login(String username, String password) async {
     try {
      var queryParameters = {
        "username": username,
        "password": password,
      };

      var uri = Uri.https(api.domain, api.projectName + api.login);
        print(uri);
          print(queryParameters);
      Response response = await networkService.post(uri, body: queryParameters);

      Map data = jsonDecode(response.body);
      print(response.body);
      if (data['code'] == 200) {
        Box box = await Hive.openBox('box');
        await box.put("access_token", data['data']['access_token']);
        await box.put("refresh_token", data['data']['refresh_token']);
        await box.put("is_login", true);
      }
      return data;
    } catch (e) {
      print("Errors found in login(POST): $e");
      return null;
    }
  }

  Future<Map?> updateProfile(String name, String phoneNumber, String email) async {
    try {

      var queryParameters = {
        "name": name,
        "phone": phoneNumber,
        "email": email,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.meUpdate);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateProfile(name, phoneNumber, email);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateProfile(POST): $e");
      return null;
    }
  }

  Future<Map?> updateToken(String? token) async {
    try {

      var queryParameters = {
        "token": token
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.tokenUpdate);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return updateToken(token);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in updateToken(POST): $e");
      return null;
    }
  }

  Future<Map?> removeToken(String? token) async {
    try {

      var queryParameters = {
        "token": token
      };
      print("Removing token: $token");
      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.removeToken);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return removeToken(token);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in removeToken(POST): $e");
      return null;
    }
  }

  Future<Map?> meUpdatePassword(
      String current, String newPass, String confirmNew) async {
    try {
      var queryParameters = {
        "current_password": current,
        'new_password': newPass,
        "new_password_confirmation": confirmNew,
      };

      Box box = Hive.box('box');
      final String accessToken = box.get("access_token") ?? "";

      var uri = Uri.https(api.domain, api.projectName + api.meUpdatePassword);
      Response response = await networkService.post(uri,
          headers: {HttpHeaders.authorizationHeader: 'Bearer $accessToken'},
          body: queryParameters);

      Map data = jsonDecode(response.body);
      if (data['code'] == 403) {
        int? refreshCode = await ApiPost().accessTokenRefresh();
        if (refreshCode == 200) {
          return meUpdatePassword(current, newPass, confirmNew);
        }
      }
      return data;
    } catch (e) {
      print("Errors found in meUpdatePassword(POST): $e");
      return null;
    }
  }
}