import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';

class ThemeColors {
  static final Box boxColor = Hive.box("boxColor");

  static Color get primaryLightGreen => Color(int.parse("0xFF8BC34A"));
  static Color get primaryLightOrange => Color(int.parse("0xFFFF9800"));
  static Color get primaryLight =>
      Color(int.parse(boxColor.get("primaryLight")));

  static Color get primaryDark => Color(int.parse("0xff0D3C36"));

  static Color get secondaryLight =>
      Color(int.parse(boxColor.get("secondaryLight")));

  static Color get secondaryDark =>
      Color(int.parse("0xfff19642"));

  static Color get dark => Color(int.parse("0xff094342"));

  static Color get gray => Color(int.parse("0xff646464"));

  static Color get light => Color(int.parse("0xff5e755c"));

  static Color get disabled => Color(int.parse("0xffd9d9d9"));


  // static Color get primaryLight =>
  //     Color(int.parse(boxColor.get("primaryLight")));

  // static Color get primaryDark => Color(int.parse(boxColor.get("primaryDark")));

  // static Color get secondaryLight =>
  //     Color(int.parse(boxColor.get("secondaryLight")));

  // static Color get secondaryDark =>
  //     Color(int.parse(boxColor.get("secondaryDark")));

  // static Color get dark => Color(int.parse(boxColor.get("dark")));

  // static Color get gray => Color(int.parse(boxColor.get("gray")));

  // static Color get light => Color(int.parse(boxColor.get("light")));

  // static Color get disabled => Color(int.parse(boxColor.get("disabled")));

}
