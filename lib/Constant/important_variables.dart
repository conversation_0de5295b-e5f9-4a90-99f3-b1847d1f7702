import 'package:maeko/services/api.dart' as API;

// const String projectName = "steven_curry";
// const String projectName = "sbk";
// const String projectName = "easi";
// const String projectName = "voker";
const String projectName = "maeko";

// const String secret = "4qJvxn0Vy14Edva28pgeyUVQDJLnnfonUDCZEY7TNSPRZW0q3WFKfDKnLK8w"; //steven curry
// const String secret = "yv5R80UTiRPDaeNy3DETs4KGmMzBEEd3YhLfkE9vo0nCyuNIX8xI2BeO47Eq"; //sbk
// const String secret = "j7DskIrRUdSPIumgBNsB6hMahVScxQu6yrKG6gvSOsYXjIIibOX0H6918iao"; //easi
// const String secret = "vqumJkZmQxv7G65mvM6ZitBRkj5kbk9ljUV7cGGULQY4wwAclSV9mZwQHkpl"; //voker
// const String secret = "1YMn0yLJHI1lbYczWpmwUakRiZ65YtDDdkTXdfwGys2gRnipDLOag70rriJw"; //voker demo
const String secret =
    "IOYU2ZZKaoAa1Nkv4CeLS62duFOvnvmNvhwxcYjZynfgC0kWkdGX0fRvXwvm"; //obriens

// const String inFrontApi = "/api/public/IVK"; // steven curry
// const String inFrontApi = "/api/public/sbk"; //sbk
// const String inFrontApi = "/api/public/easi"; //easi
// const String inFrontApi = "/api/public/voker"; //voker
// const String inFrontApi = "/api/public/VK"; //voker demo
const String inFrontApi = "/api/public/maeko"; //maeko

// Loading Brand screen's image //
// const String brandScreen = "assets/images/brand_page_sbk.png"; //sbk
// const String brandScreen = "assets/images/brand_page_obriens.png"; //obriens
const String brandScreen = "assets/images/app_icon.png"; //obriens

// Loading Brand screen's GIF //
// const String loadingGif = "assets/images/loading_animation_sbk.gif"; //sbk
// const String loadingGif =
//     "assets/images/loading_animation_obriens.gif"; //obriens
const String loadingGif =
    "assets/images/loading_animation_obriens_1.gif"; //obriens

// Splash screen's image //
// const String loadingImage = "assets/images/loading_page_steven_curry.png"; //steven curry
// const String loadingImage = "assets/images/loading_page_sbk.jpg"; //sbk
// const String loadingImage = "assets/images/loading_page_easi.png"; //easi
// const String loadingImage = "assets/images/loading_page_voker.png"; //voker
// const String loadingImage = "assets/images/loading_page_obriens.png"; //obriens
const String loadingImage =
    "assets/images/loading_page_obriens_1.png"; //obriens

const String paymentCallback =
    "https://${API.domain}/payments/redirect"; // test
// const String paymentCallback = "https://${API.domain}/${API.projectName}/api/public/stripe"; // test
// const String paymentCallback = "https://${API.domain}/stripe"; // live

const String powerBy = "Powered by Voker"; //voker
// const String powerBy = "Powered by EASI POS"; //easipos

const String currency = "RM "; //voker
// const String currency = ""; //easipos
