import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:google_fonts/google_fonts.dart';

//big
String? fontFamily1 = GoogleFonts.wendyOne().fontFamily;

//small
String? fontFamily2 = GoogleFonts.montserrat().fontFamily;

ThemeData theme(BuildContext context) {
  return ThemeData(
    textTheme: GoogleFonts.montserratTextTheme(
      Theme.of(context).textTheme,
    ),
    visualDensity: VisualDensity.adaptivePlatformDensity,
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.white,
      elevation: 0.0,
      iconTheme: const IconThemeData(color: Colors.black),
      centerTitle: true,
      titleTextStyle: TextStyle(
        color: Colors.black,
        fontSize: h2,
      ),
    ),
  );
}
  