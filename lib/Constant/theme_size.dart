import 'package:flutter/material.dart';

// phone size
double phoneWidth = MediaQueryData.fromView(
        WidgetsBinding.instance.platformDispatcher.views.single)
    .size
    .width;
double phoneHeight = MediaQueryData.fromView(
        WidgetsBinding.instance.platformDispatcher.views.single)
    .size
    .height;

// font size
double h0 = 20; // title 1
double h1 = 34; // title 1
double h2 = 16; // title 2
double h3 = 14; // headline
double h4 = 12; // body
double h5 = 10; // body
double h6 = 8; // body

// padding
double defaultPadding = 16;
double defaultInnerPadding = 12;
double topPaddingWithoutBar = 44;
double bottomPaddingWithoutBar = 34;
double horizontalPaddingMedium = 30;
double verticalPaddingSmall = 25;
double verticalPaddingMedium = 82;
double verticalPaddingLarge = 100;

// button
double buttonHeight = phoneHeight * 0.06; // 48;

// sizedBox
double spacingHeightSmall = phoneHeight * 0.006;
double spacingHeightMedium = phoneHeight * 0.018;
double spacingHeightLarge = phoneHeight * 0.03;
double spacingWidth = phoneWidth * 0.03;
