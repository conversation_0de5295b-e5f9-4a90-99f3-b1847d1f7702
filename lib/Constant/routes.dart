import 'package:flutter/material.dart';
import 'package:maeko/screens/login_part.dart';
import 'package:maeko/screens/home_page.dart';
import 'package:maeko/main.dart';
import 'package:maeko/screens/widgets/dashboard_page.dart';
import 'package:maeko/screens/widgets/impact_page.dart';
import 'package:maeko/screens/widgets/notifications_page.dart';
import 'package:maeko/screens/widgets/profile_page.dart';
import 'package:maeko/screens/widgets/scan_page.dart';


final Map<String, WidgetBuilder> routes = {
  // Login
  LoginPage.routeName: (context) => const LoginPage(),

  HomePage.routeName: (context) => HomePage(),
  DashboardPage.routeName: (context) => const DashboardPage(),
  ProfilePage.routeName: (context) => const ProfilePage(),
  ImpactPage.routeName: (context) => const ImpactPage(),
  NotificationsPage.routeName: (context) => const NotificationsPage(),
  ScanPage.routeName: (context) => const ScanPage(),
};
