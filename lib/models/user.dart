import 'package:hive/hive.dart';
part 'user.g.dart';

@HiveType(typeId: 0)
class User {
  User({
    this.username = '',
    this.name = '',
    this.phone = '',
    this.email = '',
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  @HiveField(0)
  String username;

  @HiveField(1)
  String name; // Stores full name from API

  @HiveField(2)
  String phone;

  @HiveField(3)
  String email;

  @HiveField(4)
  DateTime? createdAt;

  @HiveField(5)
  DateTime? updatedAt;

  @HiveField(6)
  DateTime? deletedAt;

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      username: json["username"] ?? '',
      name: json["name"] ?? '',
      phone: json["phone"] ?? '',
      email: json["email"] ?? '',
      createdAt: json["created_at"] != null
          ? DateTime.tryParse(json["created_at"])
          : null,
      updatedAt: json["updated_at"] != null
          ? DateTime.tryParse(json["updated_at"])
          : null,
      deletedAt: json["deleted_at"] != null
          ? DateTime.tryParse(json["deleted_at"])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "username": username,
        "name": name,
        "phone": phone,
        "email": email,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt?.toIso8601String(),
      };
}
