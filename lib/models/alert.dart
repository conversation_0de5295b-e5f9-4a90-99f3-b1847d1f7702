class Alert {
  final int id;
  final int notificationSettingId;
  final int outletId;
  final int adminId;
  final String sourceType;
  final int sourceId;
  final String type;
  final String channel;
  final String receiver;
  final String thresholdValue;
  final String thresholdUnit;
  final String title;
  final String message;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? readAt;
  bool isRead;

  Alert({
    required this.id,
    required this.notificationSettingId,
    required this.outletId,
    required this.adminId,
    required this.sourceType,
    required this.sourceId,
    required this.type,
    required this.channel,
    required this.receiver,
    required this.thresholdValue,
    required this.thresholdUnit,
    required this.title,
    required this.message,
    required this.createdAt,
    required this.updatedAt,
    this.readAt,
    required this.isRead,
  });

  factory Alert.fromJson(Map<String, dynamic> json) {
    return Alert(
      id: json['id'] ?? 0,
      notificationSettingId: json['notification_setting_id'] ?? 0,
      outletId: json['outlet_id'] ?? 0,
      adminId: json['admin_id'] ?? 0,
      sourceType: json['source_type'] ?? '',
      sourceId: json['source_id'] ?? 0,
      type: json['type'] ?? '',
      channel: json['channel'] ?? '',
      receiver: json['receiver'] ?? '',
      thresholdValue: json['threshold_value'] ?? '',
      thresholdUnit: json['threshold_unit'] ?? '',
      title: json['title'] ?? '',
      message: json['body'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      readAt: json['read_at'] != null ? DateTime.tryParse(json['read_at']) : null,
      isRead: json['read_at'] != null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'notification_setting_id': notificationSettingId,
      'outlet_id': outletId,
      'admin_id': adminId,
      'source_type': sourceType,
      'source_id': sourceId,
      'type': type,
      'channel': channel,
      'receiver': receiver,
      'threshold_value': thresholdValue,
      'threshold_unit': thresholdUnit,
      'title': title,
      'body': message, // Note: using 'body' to match your JSON structure
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'read_at': readAt?.toIso8601String(),
    };
  }

  // Utility methods
  /// Mark this alert as read
  void markAsRead() {
    if (!isRead) {
      isRead = true;
      // Note: You might want to update readAt here if you're tracking when it was read
      // readAt = DateTime.now();
    }
  }

  /// Mark this alert as unread
  void markAsUnread() {
    if (isRead) {
      isRead = false;
      // readAt = null; // Uncomment if you want to clear the read timestamp
    }
  }

  /// Get a human-readable time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Check if this is a late opening alert
  bool get isLateOpeningAlert => type == 'LATE_OPENING';

  /// Create a copy with updated values
  Alert copyWith({
    int? id,
    int? notificationSettingId,
    int? outletId,
    int? adminId,
    String? sourceType,
    int? sourceId,
    String? type,
    String? channel,
    String? receiver,
    String? thresholdValue,
    String? thresholdUnit,
    String? title,
    String? message,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? readAt,
    bool? isRead,
  }) {
    return Alert(
      id: id ?? this.id,
      notificationSettingId: notificationSettingId ?? this.notificationSettingId,
      outletId: outletId ?? this.outletId,
      adminId: adminId ?? this.adminId,
      sourceType: sourceType ?? this.sourceType,
      sourceId: sourceId ?? this.sourceId,
      type: type ?? this.type,
      channel: channel ?? this.channel,
      receiver: receiver ?? this.receiver,
      thresholdValue: thresholdValue ?? this.thresholdValue,
      thresholdUnit: thresholdUnit ?? this.thresholdUnit,
      title: title ?? this.title,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      readAt: readAt ?? this.readAt,
      isRead: isRead ?? this.isRead,
    );
  }

  @override
  String toString() {
    return 'Alert(id: $id, type: $type, title: $title, isRead: $isRead)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Alert &&
        other.id == id &&
        other.notificationSettingId == notificationSettingId &&
        other.outletId == outletId &&
        other.adminId == adminId &&
        other.sourceType == sourceType &&
        other.sourceId == sourceId &&
        other.type == type &&
        other.channel == channel &&
        other.receiver == receiver &&
        other.thresholdValue == thresholdValue &&
        other.thresholdUnit == thresholdUnit &&
        other.title == title &&
        other.message == message &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.readAt == readAt &&
        other.isRead == isRead;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      notificationSettingId,
      outletId,
      adminId,
      sourceType,
      sourceId,
      type,
      channel,
      receiver,
      thresholdValue,
      thresholdUnit,
      title,
      message,
      createdAt,
      updatedAt,
      readAt,
      isRead,
    );
  }
}