import 'package:maeko/models/alert.dart';

class PaginatedNotifications {
  final int currentPage;
  final List<Alert> data;
  final String? firstPageUrl;
  final String? nextPageUrl;
  final String? prevPageUrl;
  final String path;
  final int perPage;
  final int from;
  final int to;
  final int? lastPage;
  final int? total;
  final int unreadCount;
  final int readCount;

  PaginatedNotifications({
    required this.currentPage,
    required this.data,
    this.firstPageUrl,
    this.nextPageUrl,
    this.prevPageUrl,
    required this.path,
    required this.perPage,
    required this.from,
    required this.to,
    this.lastPage,
    this.total,
    required this.unreadCount,
    required this.readCount,
  });

  factory PaginatedNotifications.fromJson(Map<String, dynamic> json) {
    // Handle nested structure - notifications are under 'data' key, counts are at root level
    final notificationsData = json['notifications'] as Map<String, dynamic>? ?? json;
    
    return PaginatedNotifications(
      currentPage: notificationsData['current_page'] ?? 1,
      data: (notificationsData['data'] as List<dynamic>?)
              ?.map((item) => Alert.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      firstPageUrl: notificationsData['first_page_url'] as String?,
      nextPageUrl: notificationsData['next_page_url'] as String?,
      prevPageUrl: notificationsData['prev_page_url'] as String?,
      path: notificationsData['path'] as String? ?? '',
      perPage: notificationsData['per_page'] ?? 15,
      from: notificationsData['from'] ?? 0,
      to: notificationsData['to'] ?? 0,
      lastPage: notificationsData['last_page'] as int?,
      total: notificationsData['total'] as int?,
      unreadCount: int.tryParse(json['unread_count']?.toString() ?? '0') ?? 0,
      readCount: int.tryParse(json['read_count']?.toString() ?? '0') ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'data': data.map((alert) => alert.toJson()).toList(),
      'first_page_url': firstPageUrl,
      'next_page_url': nextPageUrl,
      'prev_page_url': prevPageUrl,
      'path': path,
      'per_page': perPage,
      'from': from,
      'to': to,
      'unread_count': unreadCount.toString(),
      'read_count': readCount.toString(),
      if (lastPage != null) 'last_page': lastPage,
      if (total != null) 'total': total,
    };
  }

  // Utility methods
  bool get hasNextPage => nextPageUrl != null && nextPageUrl!.isNotEmpty;
  
  bool get hasPrevPage => prevPageUrl != null && prevPageUrl!.isNotEmpty;
  
  bool get isFirstPage => currentPage == 1;
  
  bool get isLastPage => lastPage != null ? currentPage >= lastPage! : !hasNextPage;
  
  bool get isEmpty => data.isEmpty;
  
  bool get isNotEmpty => data.isNotEmpty;
  
  int get itemCount => data.length;
  
  /// Total count of all notifications (read + unread)
  int get totalNotificationCount => unreadCount + readCount;
  
  /// Returns the range of items being displayed (e.g., "1-30 of 119")
  String get itemRange {
    if (total != null) {
      return '$from-$to of $total';
    }
    return '$from-$to';
  }
  
  /// Returns notification counts summary
  String get notificationSummary => '$unreadCount unread, $readCount read';
  
  /// Check if there are any unread notifications
  bool get hasUnreadNotifications => unreadCount > 0;
  
  /// Gets unread notifications only
  List<Alert> get unreadNotifications {
    return data.where((alert) => alert.readAt == null).toList();
  }
  
  /// Gets read notifications only  
  List<Alert> get readNotifications {
    return data.where((alert) => alert.readAt != null).toList();
  }
  
  /// Groups notifications by type
  Map<String, List<Alert>> get groupedByType {
    final Map<String, List<Alert>> grouped = {};
    for (final alert in data) {
      final type = alert.type ?? 'UNKNOWN';
      grouped[type] = (grouped[type] ?? [])..add(alert);
    }
    return grouped;
  }
  
  /// Groups notifications by date (day)
  Map<String, List<Alert>> get groupedByDate {
    final Map<String, List<Alert>> grouped = {};
    for (final alert in data) {
      if (alert.createdAt != null) {
        final dateKey = alert.createdAt!.toLocal().toString().split(' ')[0];
        grouped[dateKey] = (grouped[dateKey] ?? [])..add(alert);
      }
    }
    return grouped;
  }
  
  /// Creates a copy with updated data (useful for pagination)
  PaginatedNotifications copyWith({
    int? currentPage,
    List<Alert>? data,
    String? firstPageUrl,
    String? nextPageUrl,
    String? prevPageUrl,
    String? path,
    int? perPage,
    int? from,
    int? to,
    int? lastPage,
    int? total,
    int? unreadCount,
    int? readCount,
  }) {
    return PaginatedNotifications(
      currentPage: currentPage ?? this.currentPage,
      data: data ?? this.data,
      firstPageUrl: firstPageUrl ?? this.firstPageUrl,
      nextPageUrl: nextPageUrl ?? this.nextPageUrl,
      prevPageUrl: prevPageUrl ?? this.prevPageUrl,
      path: path ?? this.path,
      perPage: perPage ?? this.perPage,
      from: from ?? this.from,
      to: to ?? this.to,
      lastPage: lastPage ?? this.lastPage,
      total: total ?? this.total,
      unreadCount: unreadCount ?? this.unreadCount,
      readCount: readCount ?? this.readCount,
    );
  }
  
  /// Merges data from another paginated result (useful for infinite scroll)
  PaginatedNotifications merge(PaginatedNotifications other) {
    return copyWith(
      currentPage: other.currentPage,
      data: [...data, ...other.data],
      nextPageUrl: other.nextPageUrl,
      prevPageUrl: other.prevPageUrl,
      from: from, // Keep original 'from'
      to: other.to,
      lastPage: other.lastPage,
      total: other.total,
    );
  }

  @override
  String toString() {
    return 'PaginatedNotifications(currentPage: $currentPage, '
           'itemCount: $itemCount, hasNext: $hasNextPage, '
           'hasPrev: $hasPrevPage, unread: $unreadCount, read: $readCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaginatedNotifications &&
        other.currentPage == currentPage &&
        other.data.length == data.length &&
        other.nextPageUrl == nextPageUrl &&
        other.prevPageUrl == prevPageUrl &&
        other.perPage == perPage &&
        other.from == from &&
        other.to == to &&
        other.lastPage == lastPage &&
        other.total == total &&
        other.unreadCount == unreadCount &&
        other.readCount == readCount;
  }

  @override
  int get hashCode {
    return Object.hash(
      currentPage,
      data.length,
      nextPageUrl,
      prevPageUrl,
      perPage,
      from,
      to,
      lastPage,
      total,
      unreadCount,
      readCount,
    );
  }
}