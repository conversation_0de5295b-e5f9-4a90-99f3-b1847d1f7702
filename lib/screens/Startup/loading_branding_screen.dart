import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:maeko/Constant/important_variables.dart';
import 'package:maeko/services/connectivity_wrapper.dart';
import 'package:maeko/screens/Startup/splash_screen.dart';
import 'package:maeko/main.dart';

class LoadingBrandScreen extends StatefulWidget {
  const LoadingBrandScreen({super.key});

  @override
  State<LoadingBrandScreen> createState() => _LoadingBrandScreenState();
}

class _LoadingBrandScreenState extends State<LoadingBrandScreen> {
  final splashDuration = 2;

  @override
  void initState() {
    super.initState();
    init();
  }

  Future<void> init() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none && mounted) {
      final state = navigatorKey.currentState?.context
          .findAncestorStateOfType<ConnectivityWrapperState>();
      if (state != null) {
        state.onConnectivityChanged(
            connectivityResult != ConnectivityResult.none);
      }
    } else {
      loadSplashWidget();
    }
  }

  loadSplashWidget() async {
    var duration = Duration(seconds: splashDuration);
    return Timer(duration, () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const SplashScreen()),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
             Container(
              width: 400,
              height: 400,
              child: ClipOval(
                child: Image.asset(
                  'assets/images/app_icon.png', // Replace with your asset path
                  width: 400,
                  height: 400,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 40),
          ],
        ),
        
      ),
    );
  }
}
