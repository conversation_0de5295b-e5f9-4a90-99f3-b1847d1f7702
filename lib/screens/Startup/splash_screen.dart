import 'package:flutter/material.dart';
import 'package:maeko/services/storage_service.dart';
import 'package:maeko/screens/home_page.dart';
import 'package:maeko/screens/login_part.dart';
import 'dart:async';
import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:flutter/material.dart';
import 'package:maeko/Constant/important_variables.dart';
import 'package:maeko/services/notification_service.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:maeko/Constant/theme.dart';
import 'package:maeko/models/user.dart';
import 'package:maeko/services/api_get.dart';
import 'package:maeko/services/api_post.dart';
import 'package:maeko/main.dart';
import 'package:geolocator/geolocator.dart';
import 'package:hive/hive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';


class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  final notificationService = NotificationService();
  bool isLogin = false;

   @override
  void initState() {
    super.initState();
    precessing();
  }

  precessing() async {
    await init();
    nextPage();
  }

   init() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // clear when user clear apps
    if (!Hive.isBoxOpen('boxMenu')) {
      await Hive.openBox('boxMenu');
    }
    Box boxMenu = await Hive.box('boxMenu');
    await boxMenu.deleteFromDisk();

    await Hive.openBox('boxMenu');

    Box box = await Hive.openBox('box');
    isLogin = box.get("is_login") ?? false;

    int? refreshCode = await refreshToken();
    if (refreshCode != null && refreshCode == 200) await getMe();

    // Load any previously stored token first
    await NotificationService.loadStoredToken();
    
    // Initialize notification service (this now includes all Firebase functionality)
    await notificationService.initialise();
    
    // Save/send notification token to backend
    await notificationService.saveNotificationToken();
   // await checkLocationPermission();

  }

  Future<void> nextPage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String lastVisitDate = prefs.getString("lastVisit") ?? "";
    String todayDate =
        "${DateTime.now().year}-${DateTime.now().month}-${DateTime.now().day}";


    if (mounted) {
      await prefs.setString("lastVisit", todayDate);
      
      if(isLogin){
       
          Navigator.pushNamedAndRemoveUntil(
          context,
          "/my_home_page",
          (Route<dynamic> route) => false,
          arguments: {'dailyPopUp': true},
        );
      }else{
       
         Navigator.pushNamedAndRemoveUntil(
          context,
          "/login/1",
          (Route<dynamic> route) => false,
         );
      }
    } else {
      if (mounted) {
        // used today
      
        if (todayDate == lastVisitDate) {
          
          if(isLogin){
       
              Navigator.pushNamedAndRemoveUntil(
              context,
              "/my_home_page",
              (Route<dynamic> route) => false,
              arguments: {'dailyPopUp': false},
            );
          }else{
           
            Navigator.pushNamedAndRemoveUntil(
              context,
              "/login/1",
              (Route<dynamic> route) => false,
            );
          }
        } else {
          // first time use today
          await prefs.setString("lastVisit", todayDate);
         
          if(isLogin){
             
              Navigator.pushNamedAndRemoveUntil(
              context,
              "/my_home_page",
              (Route<dynamic> route) => false,
              arguments: {'dailyPopUp': false},
            );
          }else{
            
            Navigator.pushNamedAndRemoveUntil(
              context,
              "/login/1",
              (Route<dynamic> route) => false,
            );
          }
        }
      }
    }
  }
  
  Future<int?> refreshToken() async {
    ApiPost apiPost = ApiPost();
    int? refreshCode = await apiPost.accessTokenRefresh();

    return refreshCode;
  }

  Future<Map?> getMe() async {
    Box box = await Hive.openBox("box");
    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.me();

    if (data != null && data['data'] != null) {
      User user = User.fromJson(data['data']['me']);
      box.put("user", user);
    }
    return data;
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
             Container(
              width: 400,
              height: 400,
              child: ClipOval(
                child: Image.asset(
                  'assets/images/app_icon.png', // Replace with your asset path
                  width: 400,
                  height: 400,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 40),
            const CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}