import 'package:maeko/constant/theme.dart';
import 'package:flutter/material.dart';
import '../services/api_post.dart';
import '../services/api_get.dart';
import '../services/storage_service.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:maeko/models/user.dart';
import 'home_page.dart';
import 'package:maeko/screens/Custom%20Widgets/default_dialog.dart';
import 'package:maeko/screens/Custom%20Widgets/default_button.dart';
import 'package:maeko/screens/Custom%20Widgets/default_textfield1.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hive/hive.dart';

class LoginPage extends StatefulWidget {
  static String routeName = "/login/1";
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  void login() async {
    defaultDialog(context, 200, "Login successful!", () {
        Navigator.pushNamedAndRemoveUntil(
          context,
          "/my_home_page",
          (Route<dynamic> route) => false,
          arguments: {'dailyPopUp': true},
        );
      }).show();
    // EasyLoading.show();
    // ApiPost apiPost = ApiPost();

    // Map? data = await apiPost.login(
    //   _emailController.text,
    //   _passwordController.text,
    // );

    // if (data != null) {
    //   if (data['code'] == 200) {
    //     EasyLoading.dismiss();

    //     Box box = await Hive.openBox("box");
    //     ApiGet apiGet = ApiGet();
    //     Map? meData = await apiGet.me();

    //     if (meData != null && meData['data'] != null) {
    //       User user = User.fromJson(meData['data']['me']);
    //       box.put("user", user);
    //     }

    //     defaultDialog(context, 200, "Login successful!", () {
    //       Navigator.pushNamedAndRemoveUntil(
    //         context,
    //         "/my_home_page",
    //         (Route<dynamic> route) => false,
    //         arguments: {'dailyPopUp': true},
    //       );
    //     }).show();
    //   } else {
    //     EasyLoading.dismiss();
    //     defaultDialog(context, data['code'], data['message'], () {}).show();
    //   }
    // } else {
    //   EasyLoading.dismiss();
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: SingleChildScrollView(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF8BC34A), // Light green
                          Color(0xFF689F38), // Darker green
                        ],
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 60),
                        const Text(
                        'Welcome to',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.w400,
                          color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 20),
                      
                        // MAEKO Logo
                        Column(
                          children: [
                            const Text(
                              'MAEKO',
                              style: TextStyle(
                                fontSize: 72,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 2,
                              ),
                            ),
                            const Text(
                              'cares • helps • works',
                              style: TextStyle(
                                fontSize: 18,
                                fontStyle: FontStyle.italic,
                                color: Colors.white70,
                                letterSpacing: 1,
                              ),
                            ),
                            // TM symbol
                            Align(
                              alignment: Alignment.centerRight,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 20, top: 5),
                                child: const Text(
                                  '™',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 30),
                        ]
                    )
                  ),

                  // Login Form Container
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Email Field
                        const SizedBox(height: 20),
                        
                        Text(
                          'Email',
                          style: TextStyle(
                            fontSize: h3,
                            fontWeight: FontWeight.w600,
                            color: Colors.black,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DefaultTextField1(
                          controller: _emailController,
                          hintText: "eg: <EMAIL>",
                        ),                      
                        const SizedBox(height: 15),
                        
                        // Password Field
                        const Text(
                          'Password',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DefaultTextField1(
                          controller: _passwordController,
                          obscureText: true,
                          hintText: "Input your password",
                        ),
                       
                        const SizedBox(height: 15),
                        
                        // Forgot Password
                        Align(
                          alignment: Alignment.center,
                          child: TextButton(
                            onPressed: () {
                              // Handle forgot password
                            },
                            child: Text(
                              'Forgot Password?',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // Login Button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child:  DefaultButton(
                              text: "LOGIN",
                              buttonColor: ThemeColors.primaryLightOrange,
                              borderRadius: 20,
                              onPressed: () {
                                login();
                              },
                          ),
                        ),
                        const SizedBox(height: 20),
                        
                        // OR Divider
                        Row(
                          children: [
                            Expanded(
                              child: Divider(
                                color: Colors.grey[400],
                                thickness: 1,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 20),
                              child: Text(
                                'or',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Divider(
                                color: Colors.grey[400],
                                thickness: 1,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        
                        // Sign Up Button
                     
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child:  DefaultButton(
                              text: "SIGN UP",
                              buttonColor: ThemeColors.primaryLightGreen,
                              borderRadius: 20,
                              onPressed: () {
                              
                              },
                          ),
                        ),
                      ],
                    ),
                  ),

                ],
              ),
          ),
        
    );
  }
}