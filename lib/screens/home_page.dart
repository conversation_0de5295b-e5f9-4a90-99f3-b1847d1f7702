import 'package:flutter/material.dart';
import 'widgets/dashboard_page.dart';
import 'widgets/impact_page.dart';
import 'widgets/notifications_page.dart';
import 'widgets/profile_page.dart';
import 'widgets/scan_page.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:maeko/screens/Custom%20Widgets/default_button.dart';
import 'package:maeko/Constant/theme_colors.dart';

class HomePage extends StatefulWidget {
  static final GlobalKey<_HomePageState> homeKey = GlobalKey<_HomePageState>();
  static String routeName = "/my_home_page";

  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentIndex = 0;
  bool isBottomSheetOpen = false;

  void setIndex(int index) {
    setState(() => _currentIndex = index);
  }

  @override
  Widget build(BuildContext context) {
    final pages = [
      const DashboardPage(),
      const ImpactPage(),
      const NotificationsPage(),
      const ProfilePage(),
      const ScanPage(),
    ];

    return Scaffold(
      extendBody: true,
      body: Stack(
        children: [
          /// Main Pages
          IndexedStack(
            index: _currentIndex,
            children: pages,
          ),

          /// Sticky Orange Support Section (only shows when sheet is closed)
          if (_currentIndex == 0 && !isBottomSheetOpen)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: SafeArea(
                top: false,
                child: GestureDetector(
                  onTap: () => _openBottomSheet(context),
                  child: _buildSupportStatus(),
                ),
              ),
            ),
        ],
      ),

      /// Bottom Navigation Bar
      bottomNavigationBar: BottomNavigationBar(
        backgroundColor: Colors.white,
        currentIndex: _currentIndex,
        onTap: (index) => setIndex(index),
        selectedItemColor: const Color(0xFF8BC34A),
        unselectedItemColor: Colors.grey,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
        selectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(fontSize: 12),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.trending_up), label: 'Impact'),
          BottomNavigationBarItem(icon: Icon(Icons.notifications), label: 'Notifications'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
          BottomNavigationBarItem(icon: Icon(Icons.qr_code_scanner), label: 'Scan'),
        ],
      ),
    );
  }

  /// 🔹 Opens the sliding bottom panel
void _openBottomSheet(BuildContext context) {
  setState(() => isBottomSheetOpen = true);

  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    barrierColor: Colors.black54, // dim background
    builder: (_) {
      return GestureDetector(
        onTap: () {
          // Close sheet when tapping outside
          Navigator.of(context).pop();
        },
        child: Container(
          color: Colors.transparent, // needed to detect outside taps
          child: GestureDetector(
            onTap: () {}, // absorb taps inside the sheet
            child: DraggableScrollableSheet(
              initialChildSize: 0.25,
              minChildSize: 0.25,
              maxChildSize: 0.45,
              builder: (context, scrollController) {
                return Container(
                  decoration: const BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                  ),
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Drag handle
                        Container(
                          width: 50,
                          height: 5,
                          decoration: BoxDecoration(
                            color: Colors.white54,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),

                        const SizedBox(height: 12),

                        /// Support + Machine Status
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            buildStatusSection(
                              icon: Icons.headset_mic,
                              title: "Support Status",
                              code: "MCS-000332",
                              status: "PENDING",
                              statusColor: Colors.orange,
                            ),
                            buildStatusSection(
                              icon: Icons.desktop_windows,
                              title: "Machine Status",
                              code: "CW200",
                              status: "OK",
                              statusColor: Colors.green,
                            ),
                          ],
                        ),

                        const SizedBox(height: 20),

                        /// Four Compact Status Items
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              buildStatusItem('5', 'PENDING'),
                              buildStatusItem('3', 'SCHEDULED'),
                              buildStatusItem('1', 'REVIEWING'),
                              buildStatusItem('2', 'PROCESSING'),
                            ],
                          ),
                        ),

                        const SizedBox(height: 20),

                        /// Buttons
                        Row(
                          children: [
                            Expanded(
                              child: DefaultButton(
                                text: "Create Ticket",
                                buttonColor: Colors.white,
                                textColor: Colors.black,
                                borderRadius: 20,
                                onPressed: () {},
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: DefaultButton(
                                text: "Add Machine",
                                buttonColor: Colors.white,
                                textColor: Colors.black,
                                borderRadius: 20,
                                onPressed: () {},
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      );
    },
  ).whenComplete(() {
    // Show sticky panel again when sheet closes
    setState(() => isBottomSheetOpen = false);
  });
}


  /// 🔹 Top Status Section → Icon + Title + Code + Badge
  Widget buildStatusSection({
    required IconData icon,
    required String title,
    required String code,
    required String status,
    required Color statusColor,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: Colors.white, size: 22),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AutoSizeText(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              minFontSize: 8,
              overflow: TextOverflow.ellipsis,
            ),
            AutoSizeText(
              code,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
              maxLines: 1,
              minFontSize: 7,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        const SizedBox(width: 6),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: AutoSizeText(
            status,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 9,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            minFontSize: 7,
          ),
        ),
      ],
    );
  }

  /// 🔹 Individual Status Item → Compact Vertical Layout
  Widget buildStatusItem(String number, String label) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          number,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 11,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 8,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// 🔹 Sticky Panel UI
  Widget _buildSupportStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: const BoxDecoration(
        color: Colors.orange,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Center(
          child: const Icon(
            Icons.keyboard_arrow_up,
            color: Colors.white,
            size: 20,
          ),
        ),
        // Existing row with support + machine status
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            buildStatusSection(
              icon: Icons.headset_mic,
              title: "Support Status",
              code: "MCS-000332",
              status: "PENDING",
              statusColor: Colors.orange,
            ),
            buildStatusSection(
              icon: Icons.desktop_windows,
              title: "Machine Status",
              code: "CW200",
              status: "OK",
              statusColor: Colors.green,
            ),
          ],
        ),
      ],
    ),
    );
  }
}
