import 'package:maeko/constant/theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:maeko/services/api_post.dart';
import 'package:maeko/screens/Custom%20Widgets/default_button.dart';
import 'package:maeko/screens/Custom%20Widgets/default_dialog.dart';
import 'package:maeko/screens/Custom%20Widgets/default_textfield1.dart';
import 'package:hive/hive.dart';
import 'package:collection/collection.dart';

class ChangePasswordPage extends StatefulWidget {
  static String routeName = "/change_password/1";

  const ChangePasswordPage({Key? key}) : super(key: key);

  @override
  _ChangePasswordPageState createState() =>
      _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  late Future future;
  late Box boxGlobal;

  TextEditingController currentPassController = TextEditingController();
  TextEditingController newPassController = TextEditingController();
  TextEditingController confirmPassController = TextEditingController();

  @override
  void initState() {
    super.initState();
    future = openBox();
  }

  openBox() async {
    boxGlobal = await Hive.openBox("boxGlobal");
    return boxGlobal;
  }

   Widget _buildPasswordRequirement(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("• ", style: TextStyle(color: Colors.grey[600])),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: h3,
                fontFamily: fontFamily2,
              ),
            ),
          ),
        ],
      ),
    );
  }


  

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final Box list = snapshot.data;
            list.watch();

            return GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              behavior: HitTestBehavior.translucent,
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  title: Text(
                    "Change Password",
                    style: TextStyle(
                      color:
                          ThemeColors.primaryDark,
                    ),
                  ),
                  iconTheme: IconThemeData(
                    color: ThemeColors.primaryDark,
                  ),
                ),
                body: Container(
                  decoration: BoxDecoration(
                          color: Color(int.parse(
                                  "0xff${"FFFFFF"}")),
                        ), 
                  height: phoneHeight,
                  child: SingleChildScrollView(
                    padding: EdgeInsets.fromLTRB(defaultPadding,
                        verticalPaddingLarge, defaultPadding, defaultPadding),
                    physics: const ClampingScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Fill in the blanks",
                          style: TextStyle(
                              color: ThemeColors.primaryDark,
                              fontSize: h2),
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField1(
                          controller: currentPassController,
                          obscureText: true,
                          hintText: "Current Password",
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField1(
                          controller: newPassController,
                          obscureText: true,
                          hintText: "New Password",
                        ),
                        SizedBox(height: spacingHeightMedium),
                        DefaultTextField1(
                          controller: confirmPassController,
                          obscureText: true,
                          hintText: "New Password Confirmation",
                        ),
                        SizedBox(height: spacingHeightMedium),
                         Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildPasswordRequirement("Password should be 8-20 characters"),
                                _buildPasswordRequirement("Password should have an upper case letter"),
                                _buildPasswordRequirement("Password should have a lower case letter"),
                                _buildPasswordRequirement("Password should have a number or acceptable character \$ ! # & @ ? % = _"),
                              ],
                            ),
                      ],
                    ),
                  ),
                ),
                bottomNavigationBar: Padding(
                  padding: EdgeInsets.fromLTRB(
                    defaultInnerPadding,
                    defaultInnerPadding,
                    defaultInnerPadding,
                    bottomPaddingWithoutBar,
                  ),
                  child: DefaultButton(
                    text: "SUBMIT",
                    buttonColor: Colors.blue,
                    textColor: Colors.white,
                    onPressed: () {
                      changePassword();
                    },
                  ),
                ),
              ),
            );
          }
          return Scaffold(appBar: AppBar());
        });
  }

  void changePassword() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();

    Map? data = await apiPost.meUpdatePassword(currentPassController.text,
        newPassController.text, confirmPassController.text);
    EasyLoading.dismiss();

    if (data != null) {
      if (data['code'] == 200) {
        defaultDialog(context, data['code'], data['message'], () {
          Navigator.of(context).pop();
        }).show();
      } else {
        defaultDialog(context, data['code'], data['message'], () {}).show();
      }
    } else {
      defaultErrorDialog(context).show();
    }
  }
}
