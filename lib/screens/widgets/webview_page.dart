// lib/widgets/webview_page.dart
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:maeko/services/storage_service.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late WebViewController controller;
  String? authToken;
  bool showComingSoon = true; // Toggle this to switch between coming soon and webview

  @override
  void initState() {
    super.initState();
    _loadAuthToken();
    _initializeWebView();
  }

  Future<void> _loadAuthToken() async {
    final token = await StorageService.getAuthToken();
    setState(() {
      authToken = token;
    });
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(
        Uri.parse('https://www.google.com'),
        headers: authToken != null ? {'Authorization': 'Bearer $authToken'} : {},
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          "Dashboard",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: showComingSoon ? _buildComingSoonWidget() : WebViewWidget(controller: controller),
    );
  }

  Widget _buildComingSoonWidget() {
    return Center(
      child: Image.asset(
        'assets/images/Image.png', // <-- your image path
        width: 300, // adjust size as you like
        height: 300,
        fit: BoxFit.contain,
      ),
    );
  }


  // Widget _buildComingSoonWidget() {
  //   return Center(
  //     child: Column(
  //       mainAxisAlignment: MainAxisAlignment.center,
  //       children: [
  //         Icon(
  //           Icons.construction,
  //           size: 80,
  //           color: Colors.grey.shade400,
  //         ),
  //         const SizedBox(height: 24),
  //         Text(
  //           'Coming Soon',
  //           style: TextStyle(
  //             fontSize: 32,
  //             fontWeight: FontWeight.bold,
  //             color: Colors.grey.shade700,
  //           ),
  //         ),
  //         const SizedBox(height: 12),
  //         Text(
  //           'Dashboard is under development',
  //           style: TextStyle(
  //             fontSize: 16,
  //             color: Colors.grey.shade500,
  //           ),
  //         ),
  //         const SizedBox(height: 8),
  //         Text(
  //           'We\'re working hard to bring you something amazing!',
  //           style: TextStyle(
  //             fontSize: 14,
  //             color: Colors.grey.shade400,
  //           ),
  //           textAlign: TextAlign.center,
  //         ),
  //       ],
  //     ),
  //   );
  // }
}