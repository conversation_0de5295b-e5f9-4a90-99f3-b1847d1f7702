import 'package:maeko/screens/widgets/change_password_page.dart';
import 'package:maeko/screens/widgets/profile_page.dart';
import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:maeko/models/user.dart';
import 'package:maeko/services/api_post.dart';
import 'package:maeko/screens/Custom Widgets/default_dialog.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:maeko/services/notification_service.dart'; // Remove firebase_service import


class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);
  static String routeName = "/settings";

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  late Box box;
  User? user;
  bool pushNotification = false;
  String? fcmToken; 
  TextEditingController nameController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUser();
  }

  void saveLocalSettings() {
    // Save settings to local Hive storage
    box.put("pushNotification", pushNotification);
  }


   void loadLocalSettings() {
    // Load settings from local Hive storage
    pushNotification = box.get("pushNotification", defaultValue: false);
    
    // Load FCM token from merged NotificationService
    fcmToken = NotificationService.fcmToken;
    
    // If token is still null, try to get it from Hive
    if (fcmToken == null) {
      fcmToken = box.get("fcm_token");
    }
    
    print("FCM Token in Settings: $fcmToken");
  }

  Future<void> _loadUser() async {
    box = await Hive.openBox("box");
    if (box.containsKey("user")) {
      user = box.get("user");
      nameController.text = user?.name ?? "";
      loadLocalSettings(); // Load push notification setting

        // If still no token, try to get a fresh one
      if (fcmToken == null) {
        fcmToken = await NotificationService.refreshToken();
      }
      
      setState(() {});
    }
  }

  Future<void> _savePushToken() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
    
      if (fcmToken == null) {
      fcmToken = NotificationService.fcmToken ?? await NotificationService.refreshToken();
    }
    final data = await apiPost.updateToken(
      fcmToken
    );
    EasyLoading.dismiss();

    if (data != null && data['code'] == 200) {

      defaultDialog(context, data['code'], data['message'], () {
      //  Navigator.pop(context);
      }).show();
    } else {
      defaultErrorDialog(context).show();
    }
  }

  Future<void> _removePushToken() async {
    EasyLoading.show();
    ApiPost apiPost = ApiPost();
       if (fcmToken == null) {
      fcmToken = NotificationService.fcmToken ?? await NotificationService.refreshToken();
    }
    final data = await apiPost.removeToken(fcmToken);
    EasyLoading.dismiss();

    if (data != null && data['code'] == 200) {

      defaultDialog(context, data['code'], data['message'], () {
       // Navigator.pop(context);
      }).show();
    } else {
      defaultErrorDialog(context).show();
    }
  }

  void _logout() async {
    await box.clear();
    Navigator.of(context).pushReplacementNamed("/login/1");  
  }

  Widget _buildProfileField(String label, TextEditingController controller,
      {bool readOnly = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            controller: controller,
            readOnly: readOnly,
            decoration: const InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSettingsItem({
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 1),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.blue,
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        trailing: trailing ??
            const Icon(
              Icons.chevron_right,
              color: Colors.grey,
            ),
        onTap: onTap,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          "Setting",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () async {
              // Navigate to ProfilePage
              await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => ProfilePage(),
                ),
              );

              // When returning from ProfilePage, reload pushNotification state
              setState(() {
                user = box.get("user");
                nameController.text = user?.name ?? "";
                pushNotification = box.get("pushNotification", defaultValue: false);
              });
            },
            child: const Text(
              "Edit Profile",
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

        ],
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: user == null
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Main content in Expanded widget so logout button stays at bottom
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // Profile Section
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(20),
                          margin: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                blurRadius: 6,
                                offset: const Offset(0, 3),
                              )
                            ],
                          ),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Container(
                                    width: 70,
                                    height: 70,
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade100,
                                      borderRadius: BorderRadius.circular(35),
                                    ),
                                    child: Icon(
                                      Icons.person_outline,
                                      color: Colors.blue,
                                      size: 35,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Text(
                                      nameController.text.isNotEmpty ? nameController.text : "User",
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 18,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                            ],
                          ),
                        ),
                        
                        // Settings Section
                        Container(
                          margin: const EdgeInsets.symmetric(horizontal: 16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                blurRadius: 6,
                                offset: const Offset(0, 3),
                              )
                            ],
                          ),
                          child: Column(
                            children: [
                              _buildSettingsItem(
                                icon: Icons.notifications_outlined,
                                title: "Push Notification",
                                trailing: Switch(
                                  value: pushNotification,
                                  onChanged: (value) {
                                    setState(() {
                                      pushNotification = value;
                                      saveLocalSettings();
                                      if(value){
                                        _savePushToken();
                                      }else{
                                        _removePushToken();
                                      }                         
                                    });
                                  },
                                  activeColor: Colors.blue,
                                ),
                              ),
                              const Divider(height: 1),
                              _buildSettingsItem(
                                icon: Icons.lock_outline,
                                title: "Change Password",
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => ChangePasswordPage(), // Pass alert ID
                                    ),
                                  );
                                  // Handle change password navigation
                                },
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 30),
                      ],
                    ),
                  ),
                ),
                
                // Logout Button - Now positioned at the bottom
                Container(
                  margin: const EdgeInsets.all(16),
                  width: double.infinity,
                  height: 50,
                  child: OutlinedButton(
                    onPressed: _logout,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red, width: 1.5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: const Text(
                      "Log out",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}