
import 'package:maeko/services/api_get.dart';
import 'package:maeko/screens/widgets/alert_detail_page.dart';
import 'package:flutter/material.dart';
import 'package:maeko/models/alert.dart';
import 'package:maeko/models/paginated_notification.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

class ImpactPage extends StatelessWidget {
  const ImpactPage({super.key});
  static String routeName = "/impact";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF8BC34A),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 24),
                _buildImpactStats(),
                const SizedBox(height: 24),
                _buildEnvironmentalImpact(),
                const SizedBox(height: 24),
                _buildMonthlyProgress(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return const Row(
      children: [
        Text('Impact', style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
        Spacer(),
        Icon(Icons.trending_up, color: Colors.white, size: 28),
      ],
    );
  }

  Widget _buildImpactStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Your Environmental Impact', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatItem('Food Saved', '2.5 kg', Icons.restaurant, Colors.orange)),
              Expanded(child: _buildStatItem('CO2 Reduced', '3.2 kg', Icons.co2, Colors.green)),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildStatItem('Water Saved', '1,250 L', Icons.water_drop, Colors.blue)),
              Expanded(child: _buildStatItem('Energy Saved', '15.6 kWh', Icons.bolt, Colors.amber)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(color: color.withOpacity(0.2), borderRadius: BorderRadius.circular(25)),
          child: Icon(icon, color: color, size: 28),
        ),
        const SizedBox(height: 8),
        Text(value, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        Text(title, style: const TextStyle(fontSize: 12, color: Colors.grey), textAlign: TextAlign.center),
      ],
    );
  }

  Widget _buildEnvironmentalImpact() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(colors: [Color(0xFF42A8CF), Color(0xFF4CAF50)]),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Global Impact Contribution', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          const Text('You\'re part of a movement that has collectively:', style: TextStyle(color: Colors.white70, fontSize: 14)),
          const SizedBox(height: 12),
          _buildGlobalStat('🌍', 'Saved 10,000+ kg of food waste'),
          _buildGlobalStat('💧', 'Conserved 500,000+ liters of water'),
          _buildGlobalStat('🌱', 'Prevented 15,000+ kg CO2 emissions'),
        ],
      ),
    );
  }

  Widget _buildGlobalStat(String emoji, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(emoji, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Text(text, style: const TextStyle(color: Colors.white, fontSize: 14)),
        ],
      ),
    );
  }

  Widget _buildMonthlyProgress() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(16)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Monthly Progress', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          _buildProgressBar('Food Waste Reduction', 0.75, Colors.orange),
          const SizedBox(height: 12),
          _buildProgressBar('Sustainability Goals', 0.60, Colors.green),
          const SizedBox(height: 12),
          _buildProgressBar('Community Impact', 0.85, Colors.blue),
        ],
      ),
    );
  }

  Widget _buildProgressBar(String label, double progress, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
        const SizedBox(height: 8),
        LinearProgressIndicator(value: progress, backgroundColor: Colors.grey.shade300, valueColor: AlwaysStoppedAnimation(color)),
        const SizedBox(height: 4),
        Text('${(progress * 100).toInt()}%', style: TextStyle(fontSize: 12, color: color, fontWeight: FontWeight.w600)),
      ],
    );
  }
}