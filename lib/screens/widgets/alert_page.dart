import 'package:maeko/services/api_get.dart';
import 'package:maeko/screens/widgets/alert_detail_page.dart';
import 'package:flutter/material.dart';
import 'package:maeko/models/alert.dart';
import 'package:maeko/models/paginated_notification.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

class AlertPage extends StatefulWidget {
  const AlertPage({super.key});
  static String routeName = "/alert";

  @override
  State<AlertPage> createState() => _AlertPageState();
}

class _AlertPageState extends State<AlertPage> {
  List<Alert> _alerts = [];
  bool _isLoading = false;
  String _filter = 'All';
  String _searchQuery = '';
  
  // Count variables
  int _totalUnreadCount = 0;
  int _totalReadCount = 0;
  int _allCount = 0;

  int _durationValue = 0;
  String _durationUnit = 'day';
  
  // Pagination variables
  int _currentPage = 1;
  bool _hasNextPage = false;
  bool _isLoadingMore = false;
  PaginatedNotifications? _paginatedData;
  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadAlerts();
    _setupScrollListener();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= 
          _scrollController.position.maxScrollExtent - 200) {
        _loadMoreAlerts();
      }
    });
  }

  // Convert filter string to API parameter (null for 'All')
  String _getApiFilterValue() {
    switch (_filter) {
      case 'Unread':
        return 'unread';
      case 'Read':
        return 'read';
      case 'All':
      default:
        return ''; // No filter parameter for 'All'
    }
  }

  Future<Map?> _loadAlerts({bool isRefresh = true}) async {
    if (isRefresh) {
      setState(() {
        _isLoading = true;
        _currentPage = 1;
        _alerts.clear();
      });
    }

    ApiGet apiGet = ApiGet();
    Map? data = await apiGet.alerts(
      _getApiFilterValue(), // Use converted filter value
      _currentPage - 1,
      keyword: _searchQuery,
      durationValue: _durationValue,
      durationUnit: _durationUnit,
    );

    if (data != null && data['data'] != null) {
      final paginatedNotifications = PaginatedNotifications.fromJson(data['data']);
      
      setState(() {
        if (isRefresh) {
          _alerts = paginatedNotifications.data;
          _paginatedData = paginatedNotifications;
        } else {
          _alerts.addAll(paginatedNotifications.data);
          _paginatedData = _paginatedData?.merge(paginatedNotifications) ?? paginatedNotifications;
        }
        
        if (isRefresh) {
          _totalUnreadCount = paginatedNotifications.unreadCount;
          _totalReadCount = paginatedNotifications.readCount;
          _allCount = paginatedNotifications.totalNotificationCount;
        }
        
        _hasNextPage = paginatedNotifications.hasNextPage;
        _currentPage = paginatedNotifications.currentPage;
      });
    }

    setState(() {
      _isLoading = false;
      _isLoadingMore = false;
    });
    return data;
  }

  Future<void> _loadMoreAlerts() async {
    if (_isLoadingMore || !_hasNextPage || _isLoading) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      ApiGet apiGet = ApiGet();
      Map? data = await apiGet.alerts(
        _getApiFilterValue(), // Use converted filter value
        _currentPage,
        keyword: _searchQuery,
        durationValue: _durationValue,
        durationUnit: _durationUnit,
      );

      if (data != null && data['data'] != null) {
        final paginatedNotifications = PaginatedNotifications.fromJson(data['data']);
        
        setState(() {
          _alerts.addAll(paginatedNotifications.data);
          _hasNextPage = paginatedNotifications.hasNextPage;
          _currentPage = paginatedNotifications.currentPage;
          _paginatedData = _paginatedData?.merge(paginatedNotifications) ?? paginatedNotifications;
        });
      }
    } catch (e) {
      print('Error loading more alerts: $e');
    }

    setState(() {
      _isLoadingMore = false;
    });
  }

  Future<void> _onAlertTap(Alert alert) async {
    EasyLoading.show();

    try {
      ApiGet apiGet = ApiGet();
      final data = await apiGet.alertDetail(alert.id);

      EasyLoading.dismiss();

      final wasUnread = !alert.isRead;

      if (data != null && data['data']?['redirect_url'] != null) {
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => AlertDetailPage(alertId: alert.id),
          ),
        );
        
        _loadAlerts(isRefresh: true);
      } else {
        setState(() {
          final alertIndex = _alerts.indexWhere((a) => a.id == alert.id);
          if (alertIndex != -1 && wasUnread) {
            _alerts[alertIndex].isRead = true;
            
            _totalUnreadCount = (_totalUnreadCount - 1).clamp(0, _totalUnreadCount);
            _totalReadCount = _totalReadCount + 1;
          }
        });
      }
    } catch (e) {
      EasyLoading.dismiss();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to load alert detail'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showFilterPopup() async {
    final selected = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        contentPadding: EdgeInsets.zero,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDurationOption('7 Days', 7, 'day'),
            const Divider(height: 0),
            _buildDurationOption('30 Days', 30, 'day'),
            const Divider(height: 0),
            _buildDurationOption('3 Months', 3, 'month'),
            const Divider(height: 0),
            _buildDurationOption('6 Months', 6, 'month'),
          ],
        ),
      ),
    );

    if (selected != null) {
      setState(() {
        _durationValue = selected['value'];
        _durationUnit = selected['unit'];
      });
      _loadAlerts(isRefresh: true);
    }
  }

  Widget _buildDurationOption(String label, int value, String unit) {
    return InkWell(
      onTap: () => Navigator.pop(context, {'value': value, 'unit': unit}),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        alignment: Alignment.center,
        child: Text(label, style: const TextStyle(fontSize: 16)),
      ),
    );
  }

  // Simplified - no local filtering since API handles it
  List<Alert> _getFilteredAlerts() {
    List<Alert> filtered = _alerts;

    // Only apply search filter locally
    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((a) =>
              a.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              a.message.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          "Alert",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _showFilterPopup,
            child: const Text(
              "Filter",
              style: TextStyle(
                color: Colors.blue,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Search Bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: InputDecoration(
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  hintText: 'Search',
                  filled: true,
                  fillColor: Colors.grey[200],
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(vertical: 0),
                ),
                onChanged: (val) {
                  setState(() => _searchQuery = val);
                  Future.delayed(const Duration(milliseconds: 500), () {
                    if (_searchQuery == val) {
                      _loadAlerts(isRefresh: true);
                    }
                  });
                },
              ),
            ),

            const SizedBox(height: 12),

            // Filter Chips with Counts
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  _buildFilterChipWithCount('All', Colors.blue, 0),
                  const SizedBox(width: 8),
                  _buildFilterChipWithCount('Unread', Colors.blue, _totalUnreadCount),
                  const SizedBox(width: 8),
                  _buildFilterChipWithCount('Read', Colors.blue, _totalReadCount),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // List
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : RefreshIndicator(
                      onRefresh: () => _loadAlerts(isRefresh: true),
                      child: _getFilteredAlerts().isEmpty
                          ? _buildEmptyState()
                          : ListView.builder(
                              controller: _scrollController,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: _getFilteredAlerts().length + (_isLoadingMore ? 1 : 0),
                              itemBuilder: (context, index) {
                                final filteredAlerts = _getFilteredAlerts();
                                
                                if (index == filteredAlerts.length) {
                                  return _buildLoadingMoreIndicator();
                                }
                                
                                final alert = filteredAlerts[index];
                                return _buildAlertCard(alert);
                              },
                            ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterChipWithCount(String label, Color color, int count) {
    final isSelected = _filter == label;
    return ChoiceChip(
      backgroundColor: Colors.white,
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label),
          if (count > 0) ...[
            const SizedBox(width: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected ? Colors.white : color,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                count.toString(),
                style: TextStyle(
                  color: isSelected ? color : Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ],
      ),
      selected: isSelected,
      selectedColor: color,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : Colors.black,
        fontWeight: FontWeight.w500,
      ),
      onSelected: (_) {
        setState(() => _filter = label);
        _loadAlerts(isRefresh: true); // This will now use the correct API filter
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No ${_filter.toLowerCase()} alerts found',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search terms',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade300),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'Loading more alerts...',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildAlertCard(Alert alert) {
    return InkWell(
      onTap: () => _onAlertTap(alert),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: _getIconBgColor(alert.type),
                borderRadius: BorderRadius.circular(50),
              ),
              child: _getIconWidget(alert.type),
            ),

            const SizedBox(width: 12),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          alert.title,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: !alert.isRead ? Colors.black : Colors.grey.shade700,
                          ),
                        ),
                      ),
                      if (!alert.isRead)
                        Container(
                          width: 10,
                          height: 10,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    alert.message,
                    style: TextStyle(
                      color: !alert.isRead ? Colors.black87 : Colors.grey.shade600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('d/M/yyyy , HH:mm:ss').format(alert.updatedAt),
                    style: const TextStyle(color: Colors.grey, fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getIconBgColor(String title) {
    if (title.contains('LATE_OPENING')) return Color(0xFF54A0FF);
    if (title.contains('CCTV_FAILURE')) return Color(0xFF54A0FF);
    if (title.contains('UNAUTHORIZED_ACCESS')) return Color(0xFF6C5CE7);
    return Color(0xFFEB4D4B);
  }

  Widget _getIconWidget(String title) {
    if (title.contains('LATE_OPENING')) {
      return Image.asset(
        'assets/icons/Late Opening.png',
        width: 22,
        height: 22,
      );
    }
    if (title.contains('CCTV_FAILURE')) {
      return Image.asset(
        'assets/icons/CCTV Failure.png',
        width: 22,
        height: 22,
      );
    }
    
    return Image.asset(
      'assets/icons/Unauthorise n intruder.png',
      width: 22,
      height: 22,
    );
  }
}