import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:maeko/services/api_get.dart';

class AlertDetailPage extends StatefulWidget {
  final int alertId;
  const AlertDetailPage({super.key, required this.alertId});
  static String routeName = "/alertDetail";

  @override
  State<AlertDetailPage> createState() => _AlertDetailPageState();
}

class _AlertDetailPageState extends State<AlertDetailPage> {
  String? _redirectUrl;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAlertDetail();
  }

  Future<void> _loadAlertDetail() async {
    ApiGet apiGet = ApiGet();
    final data = await apiGet.alertDetail(widget.alertId);

    if (data != null && data['data']?['redirect_url'] != null) {
      setState(() {
        _redirectUrl = data['data']['redirect_url'];
        print(_redirectUrl);
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to load alert detail')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          "Alert Detail",
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 18,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _redirectUrl != null
              ? WebViewWidget(
                  controller: WebViewController()
                    ..setJavaScriptMode(JavaScriptMode.unrestricted)
                    ..loadRequest(Uri.parse(_redirectUrl!)),
                )
              : const Center(child: Text('No URL found')),
    );
  }
}
