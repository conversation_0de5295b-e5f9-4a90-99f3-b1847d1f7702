

import 'package:maeko/services/api_get.dart';
import 'package:maeko/screens/widgets/alert_detail_page.dart';
import 'package:flutter/material.dart';
import 'package:maeko/models/alert.dart';
import 'package:maeko/models/paginated_notification.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});
  static String routeName = "/notifications";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF8BC34A),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(top: 16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                ),
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _buildNotificationItem('Machine Status Update', 'Your MunchBOT CW200 is now online and ready to use.', Icons.check_circle, Colors.green, '2 min ago'),
                    _buildNotificationItem('Support Ticket Update', 'Your support ticket MCS-000332 is being reviewed by our team.', Icons.headset_mic, Colors.orange, '1 hour ago'),
                    _buildNotificationItem('Weekly Impact Report', 'You\'ve saved 2.5kg of food waste this week! Great job!', Icons.trending_up, Colors.blue, '1 day ago'),
                    _buildNotificationItem('MPoints Earned', 'You\'ve earned 50 MPoints for consistent usage this month.', Icons.star, Colors.amber, '2 days ago'),
                    _buildNotificationItem('Maintenance Reminder', 'Schedule maintenance for your MunchBOT for optimal performance.', Icons.build, Colors.red, '3 days ago'),
                    _buildNotificationItem('New Feature Available', 'Check out the new Impact tracking feature in your dashboard.', Icons.new_releases, Colors.purple, '1 week ago'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Text('Notifications', style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(color: Colors.white.withOpacity(0.2), borderRadius: BorderRadius.circular(12)),
            child: const Text('Mark all read', style: TextStyle(color: Colors.white, fontSize: 12)),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(String title, String subtitle, IconData icon, Color color, String time) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(color: color.withOpacity(0.2), borderRadius: BorderRadius.circular(20)),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                const SizedBox(height: 4),
                Text(subtitle, style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
                const SizedBox(height: 8),
                Text(time, style: TextStyle(fontSize: 10, color: Colors.grey.shade500)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}