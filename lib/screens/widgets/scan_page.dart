
import 'package:maeko/services/api_get.dart';
import 'package:maeko/screens/widgets/alert_detail_page.dart';
import 'package:flutter/material.dart';
import 'package:maeko/models/alert.dart';
import 'package:maeko/models/paginated_notification.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:intl/intl.dart';

class ScanPage extends StatefulWidget {
  const ScanPage({super.key});
  static String routeName = "/scan";

  @override
  State<ScanPage> createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> {
  bool isScanning = false;
  String scannedCode = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF8BC34A),
      body: SafeArea(
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: Container(
                margin: const EdgeInsets.only(top: 16),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      _buildScannerArea(),
                      const SizedBox(height: 32),
                      _buildScanOptions(),
                      if (scannedCode.isNotEmpty) ...[
                        const SizedBox(height: 24),
                        _buildScannedResult(),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const Text('Scan QR Code', style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold)),
          const Spacer(),
          IconButton(
            onPressed: () {},
            icon: const Icon(Icons.flash_on, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildScannerArea() {
    return Container(
      height: 300,
      width: 300,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF8BC34A), width: 3),
      ),
      child: isScanning
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(color: Color(0xFF8BC34A)),
                const SizedBox(height: 16),
                Text('Scanning...', style: TextStyle(color: Colors.grey.shade600, fontSize: 16)),
              ],
            )
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.qr_code_scanner, size: 80, color: Colors.grey.shade400),
                const SizedBox(height: 16),
                Text('Position QR code within the frame', style: TextStyle(color: Colors.grey.shade600, fontSize: 14), textAlign: TextAlign.center),
              ],
            ),
    );
  }

  Widget _buildScanOptions() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              setState(() {
                isScanning = true;
              });
              // Simulate scanning
              Future.delayed(const Duration(seconds: 2), () {
                setState(() {
                  isScanning = false;
                  scannedCode = 'MAEKO-CW200-001';
                });
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF8BC34A),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            child: Text(isScanning ? 'Scanning...' : 'Start Scanning', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.photo_library, size: 20),
                label: const Text('From Gallery'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF8BC34A),
                  side: const BorderSide(color: Color(0xFF8BC34A)),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.input, size: 20),
                label: const Text('Manual Entry'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF8BC34A),
                  side: const BorderSide(color: Color(0xFF8BC34A)),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScannedResult() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF8BC34A).withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF8BC34A).withOpacity(0.3)),
      ),
      child: Column(
        children: [
          const Icon(Icons.check_circle, color: Color(0xFF8BC34A), size: 40),
          const SizedBox(height: 12),
          const Text('Successfully Scanned!', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Color(0xFF8BC34A))),
          const SizedBox(height: 8),
          Text('Machine ID: $scannedCode', style: TextStyle(fontSize: 14, color: Colors.grey.shade700)),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF8BC34A),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('Add Machine'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      scannedCode = '';
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF8BC34A),
                    side: const BorderSide(color: Color(0xFF8BC34A)),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('Scan Again'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 