import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';
import 'package:shimmer/shimmer.dart';

class Loading2 extends StatelessWidget {
  Loading2({Key? key}) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      height: phoneHeight,
      padding: EdgeInsets.fromLTRB(
        defaultPadding,
        topPaddingWithoutBar,
        defaultPadding,
        defaultPadding,
      ),
      child: Shimmer.fromColors(
        baseColor: ThemeColors.disabled,
        highlightColor: ThemeColors.light,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
              height: buttonHeight,
              decoration: BoxDecoration(
                color: ThemeColors.light,
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
                decoration: BoxDecoration(
                  color: ThemeColors.light,
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
