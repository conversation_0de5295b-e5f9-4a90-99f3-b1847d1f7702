import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_size.dart';

class CustomAlertDialog extends StatelessWidget {
  final BuildContext context;
  final String title;
  final String message;
  final String buttonText;
  final String image;
  final Color iconColor;
  final Color iconBackground;
  final VoidCallback onPressed;

  CustomAlertDialog({
    required this.context,
    required this.title,
    required this.message,
    required this.buttonText,
    required this.image,
    required this.iconColor,
    required this.iconBackground,
    required this.onPressed,
  });

  void show() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => this,
    );
  }

  void handleOnPressed() {
    Navigator.of(context).pop();
    onPressed();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: EdgeInsets.all(horizontalPaddingMedium),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: SizedBox(
              width: 80,
              height: 80,
              child: Image.asset(
                image,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: h1,
              fontWeight: FontWeight.bold,
              color: iconColor,
            ),
          ),
          SizedBox(height: 10),
          Text(
            message,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: h4),
          ),
          SizedBox(height: 20),
          SizedBox(
            width: phoneWidth / 3,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: iconColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              onPressed: handleOnPressed,
              child: Text(
                buttonText,
                style: TextStyle(
                  color: Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
