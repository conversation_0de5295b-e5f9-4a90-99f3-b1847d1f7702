import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultTextField2 extends StatefulWidget {
  final TextEditingController controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final TextInputType keyBoardType;
  final int maxLines;
  final bool readOnly;
  final bool passwordField;
  final Color? fieldColor;
  final Function(String)? onChanged;

  DefaultTextField2({
    Key? key,
    required this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.keyBoardType = TextInputType.text,
    this.maxLines = 1,
    this.readOnly = false,
    this.passwordField = false,
    this.fieldColor,
    this.onChanged,
  }) : super(key: key);

  @override
  State<DefaultTextField2> createState() => _DefaultTextField2State();
}

class _DefaultTextField2State extends State<DefaultTextField2> {
  final Box boxGlobal = Hive.box("boxGlobal");
  bool passwordVisible = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: widget.fieldColor ?? ThemeColors.light,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.labelText != null)
            Text(
              widget.labelText!,
              style: TextStyle(
                color: ThemeColors.primaryDark,
                fontSize: h3,
                fontWeight: FontWeight.w600,
              ),
            ),
          Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ThemeData()
                  .colorScheme
                  .copyWith(primary: ThemeColors.primaryDark),
            ),
            child: TextField(
              controller: widget.controller,
              onChanged: widget.onChanged,
              keyboardType: widget.keyBoardType,
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
              maxLines: widget.maxLines,
              readOnly: widget.readOnly,
              obscureText: widget.passwordField ? !passwordVisible : false,
              cursorColor: ThemeColors.primaryDark,
              decoration: InputDecoration(
                hintText: widget.hintText,
                hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
                helperText: widget.helperText,
                helperStyle: TextStyle(color: ThemeColors.secondaryDark),
                helperMaxLines: 3,
                contentPadding: EdgeInsets.all(defaultInnerPadding),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: ThemeColors.disabled),
                ),
                suffixIcon: widget.passwordField
                    ? IconButton(
                        icon: Icon(
                          passwordVisible
                              ? Icons.visibility
                              : Icons.visibility_off,
                        ),
                        onPressed: () {
                          setState(() {
                            passwordVisible = !passwordVisible;
                          });
                        },
                      )
                    : null,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
