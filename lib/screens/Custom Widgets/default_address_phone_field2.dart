import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultAddressPhoneField2 extends StatefulWidget {
  final TextEditingController controller;
  final String labelText;
  final TextInputType? textInputType;
  final String selectedCountryCode;

  const DefaultAddressPhoneField2({
    Key? key,
    required this.controller,
    this.labelText = "Phone Number",
    this.textInputType,
    required this.selectedCountryCode,
  }) : super(key: key);

  @override
  State<DefaultAddressPhoneField2> createState() =>
      _DefaultAddressPhoneField2State();
}

class _DefaultAddressPhoneField2State extends State<DefaultAddressPhoneField2> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.labelText,
            style: TextStyle(
              color: ThemeColors.primaryDark,
              fontSize: h3,
              fontWeight: FontWeight.w600,
            ),
          ),
          Row(
            children: [
              Container(
                width: phoneWidth / 5,
                child: Text(
                  widget.selectedCountryCode,
                  style: TextStyle(
                    color: ThemeColors.dark,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                child: Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: ThemeData()
                        .colorScheme
                        .copyWith(primary: ThemeColors.primaryDark),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    keyboardType: widget.textInputType,
                    cursorColor: ThemeColors.primaryDark,
                    decoration: InputDecoration(
                      hintText: "1********",
                      hintStyle:
                          TextStyle(color: ThemeColors.gray, fontSize: h3),
                      contentPadding: EdgeInsets.all(defaultInnerPadding),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: ThemeColors.disabled),
                      ),
                    ),
                    style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
