import 'package:flutter/material.dart';
import 'package:maeko/screens/Custom%20Widgets/custom_alert_dialog.dart';
import 'package:hive/hive.dart';

Box boxGlobal = Hive.box('boxGlobal');
final String success = "SUCCESS";
final String warning = "WARNING";
final String error = "ERROR";
final String buttonText = "OK";
final Color greenColor = Color.fromARGB(255, 4, 201, 47);
final Color redColor = Color(0xFFD02F6B);
final Color yellowColor = Color(0xFFFCC54F);

CustomAlertDialog defaultDialog(
    BuildContext context, int code, String message, VoidCallback onPressed) {
  return CustomAlertDialog(
    context: context,
    title: code == 200 ? success : warning,
    message: message,
    buttonText: buttonText,
    image: code == 200
        ? "assets/dialogs/success.gif"
        : "assets/dialogs/warning.gif",
    iconColor: code == 200 ? Colors.blue : yellowColor,
    iconBackground: code == 200 ? Colors.blue : yellowColor,
    onPressed: onPressed,
  );
}

CustomAlertDialog defaultWarningDialog(
    BuildContext context, String message, VoidCallback onPressed) {
  return CustomAlertDialog(
    context: context,
    title: error,
    message: message,
    buttonText: buttonText,
    image: "assets/dialogs/warning.gif",
    iconColor: yellowColor,
    iconBackground: yellowColor.withOpacity(0.2),
    onPressed: onPressed,
  );
}

CustomAlertDialog defaultErrorDialog(BuildContext context) {
  return CustomAlertDialog(
    context: context,
    title: error,
    message: "Something went wrong. Please try again.",
    buttonText: buttonText,
    image: "assets/dialogs/error.gif",
    iconColor: redColor,
    iconBackground: redColor.withOpacity(0.2),
    onPressed: () {},
  );
}
