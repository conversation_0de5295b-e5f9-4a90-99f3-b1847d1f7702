import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultDropdown2 extends StatelessWidget {
  final String selectedValue;
  final String? labelText;
  final List<DropdownMenuItem<String>>? items;
  final Function(String?)? onChanged;

  DefaultDropdown2({
    Key? key,
    required this.selectedValue,
    this.labelText,
    required this.items,
    required this.onChanged,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
        decoration: BoxDecoration(
          color: ThemeColors.light,
          border: Border(bottom: BorderSide(color: ThemeColors.disabled)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (labelText != null)
              Text(
                labelText!,
                style: TextStyle(color: ThemeColors.primaryDark, fontSize: h3),
              ),
            DropdownButton2(
              buttonStyleData: ButtonStyleData(
                height: buttonHeight,
              ),
              items: items,
              value: selectedValue,
              onChanged: onChanged,
              isExpanded: true,
              underline: Container(),
              iconStyleData: IconStyleData(
                iconEnabledColor: ThemeColors.dark,
              ),
            ),
          ],
        ));
  }
}
