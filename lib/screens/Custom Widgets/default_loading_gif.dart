import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:maeko/Constant/theme_size.dart';

class DefaultLoadingGif extends StatelessWidget {
  DefaultLoadingGif();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      width: 120,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(80.0),
        child: Image.asset(
          'assets/images/loading_animation_obriens_1.gif',
        ),
      ),
    );
  }
}
