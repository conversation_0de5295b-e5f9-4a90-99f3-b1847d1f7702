import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultDropdown1<T> extends StatelessWidget {
  final T selectedValue;
  final List<DropdownMenuItem<T>>? items;
  final ValueChanged<T?>? onChanged;
  final double verticalMargin;
  final double borderRadius;

  DefaultDropdown1({
    Key? key,
    required this.selectedValue,
    required this.items,
    required this.onChanged,
    this.verticalMargin = 12,
    this.borderRadius = 30,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: verticalMargin),
      child: DropdownButton2<T>(
        buttonStyleData: ButtonStyleData(
          padding: EdgeInsets.only(right: 5),
          height: buttonHeight,
          decoration: BoxDecoration(
            color: ThemeColors.light,
            borderRadius: BorderRadius.circular(borderRadius),
            border: Border.all(color: ThemeColors.disabled),
          ),
        ),
        iconStyleData: IconStyleData(icon: Icon(Icons.keyboard_arrow_down)),
        items: items,
        value: selectedValue,
        onChanged: onChanged,
        isExpanded: true,
        underline: Container(),
      ),
    );
  }
}
