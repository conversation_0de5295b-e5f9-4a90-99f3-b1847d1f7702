import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultAddressPhoneField1 extends StatefulWidget {
  final TextEditingController controller;
  final String? hintText;
  final TextInputType? textInputType;
  final String selectedCountryCode;

  const DefaultAddressPhoneField1({
    Key? key,
    required this.controller,
    this.hintText,
    this.textInputType,
    required this.selectedCountryCode,
  }) : super(key: key);

  @override
  State<DefaultAddressPhoneField1> createState() =>
      _DefaultAddressPhoneField1State();
}

class _DefaultAddressPhoneField1State extends State<DefaultAddressPhoneField1> {
  Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: Row(
        children: [
          Container(
            width: phoneWidth / 5,
            decoration: BoxDecoration(
              border: Border(
                  right: BorderSide(
                color: ThemeColors.disabled,
              )),
            ),
            child: Text(
              widget.selectedCountryCode,
              style: TextStyle(
                color: ThemeColors.dark,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: TextField(
              controller: widget.controller,
              cursorColor: ThemeColors.primaryDark,
              keyboardType: widget.textInputType,
              decoration: InputDecoration(
                hintText: "1********",
                hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
                contentPadding: EdgeInsets.all(defaultInnerPadding),
                border: InputBorder.none,
              ),
              style: TextStyle(color: ThemeColors.dark, fontSize: h3),
            ),
          ),
        ],
      ),
    );
  }
}
