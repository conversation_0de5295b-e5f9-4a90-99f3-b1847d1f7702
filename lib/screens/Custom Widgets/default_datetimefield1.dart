import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultDateTimeField1 extends StatelessWidget {
  final TextEditingController controller;
  final Function()? onTap;
  final String? hintText;

  DefaultDateTimeField1({
    Key? key,
    required this.controller,
    required this.onTap,
    this.hintText,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: defaultInnerPadding),
      height: buttonHeight,
      decoration: BoxDecoration(
        color: ThemeColors.light,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(color: ThemeColors.disabled),
      ),
      child: TextField(
        controller: controller,
        style: TextStyle(color: ThemeColors.dark, fontSize: h3),
        readOnly: true,
        cursorColor: ThemeColors.primaryDark,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
          contentPadding: EdgeInsets.all(defaultPadding),
          border: InputBorder.none,
        ),
        onTap: onTap,
      ),
    );
  }
}
