import 'package:flutter/material.dart';
import 'package:maeko/Constant/theme_colors.dart';
import 'package:maeko/Constant/theme_size.dart';
import 'package:hive/hive.dart';

class DefaultTextFieldButton extends StatelessWidget {
  final String buttonText;
  final Function() onTap;
  final TextEditingController controller;
  final String? hintText;
  final TextInputType keyBoardType;
  final int maxLines;
  final bool readOnly;
  final bool obscureText;

  DefaultTextFieldButton({
    Key? key,
    required this.buttonText,
    required this.onTap,
    required this.controller,
    this.hintText,
    this.keyBoardType = TextInputType.text,
    this.maxLines = 1,
    this.readOnly = false,
    this.obscureText = false,
  }) : super(key: key);

  final Box boxGlobal = Hive.box("boxGlobal");

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(right: defaultInnerPadding),
        height: buttonHeight * maxLines,
        decoration: BoxDecoration(
          color: ThemeColors.light,
          borderRadius: BorderRadius.circular(30),
          border: Border.all(color: ThemeColors.disabled),
        ),
        child: Row(
          children: [
            Expanded(
              child: TextField(
                controller: controller,
                keyboardType: keyBoardType,
                style: TextStyle(color: ThemeColors.dark, fontSize: h3),
                maxLines: maxLines,
                readOnly: readOnly,
                obscureText: obscureText,
                cursorColor: ThemeColors.primaryDark,
                decoration: InputDecoration(
                  hintText: hintText,
                  hintStyle: TextStyle(color: ThemeColors.gray, fontSize: h3),
                  contentPadding: EdgeInsets.all(defaultPadding),
                  border: InputBorder.none,
                ),
              ),
            ),
            GestureDetector(
              onTap: onTap,
              child: Text(
                buttonText,
                style: TextStyle(color: ThemeColors.primaryDark, fontSize: h3),
              ),
            ),
          ],
        ));
  }
}
