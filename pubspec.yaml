name: maeko
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.27.0
  firebase_messaging: ^14.7.10
  flutter_html: ^3.0.0
  hugeicons: ^0.0.9
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  webview_flutter: ^4.4.2
  shared_preferences: ^2.2.2
  http: ^1.1.0
  flutter_easyloading: ^3.0.5
  awesome_dialog: ^3.0.2
  carousel_indicator: ^1.0.6
  flutter_cache_manager: ^3.3.0
  cached_network_image: ^3.2.0
  carousel_slider: ^4.2.1
  dots_indicator: ^3.0.0
  auto_size_text: ^3.0.0
  animated_text_kit: ^4.2.1
  dropdown_button2: ^2.1.2
  shimmer: ^2.0.0
  url_launcher: ^6.1.8
  share_plus: ^7.2.2
  sign_in_with_apple: ^6.1.1
  flutter_svg: ^2.0.10+1
  intl: ^0.18.0
  flutter_widget_from_html: ^0.10.0
  sliding_up_panel: ^2.0.0+1
  package_info_plus: ^4.2.0
  connectivity_plus: ^4.0.0
  geolocator: ^9.0.2
  pin_input_text_field: ^4.4.1
  flutter_switch: ^0.3.2
  win32: ^5.5.3
  easy_stepper: ^0.5.2+1
  rxdart: ^0.27.7
  firebase_auth: ^4.6.2
  toggle_switch: ^2.1.0
  syncfusion_flutter_gauges: ^24.2.7
  flutter_contacts: ^1.1.7+1
  decimal: ^2.3.3
  flutter_riverpod: ^2.4.10
  google_fonts: 6.1.0
  chewie: ^1.7.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.2.0
  hive_generator: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  #  image_path: "assets/icons/App Icon/steven_curry.png"
  #  image_path: "assets/icons/App Icon/sbk.jpg"
  #  image_path: "assets/icons/App Icon/easi.png"
  #  image_path: "assets/icons/App Icon/voker.png"
  image_path: "assets/icons/App Icon/app_icon.png"
  android: true
  ios: true
  remove_alpha_ios: true
  # dart run flutter_launcher_icons
  # flutter pub run flutter_launcher_icons:main

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analyflutsis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^1.0.0


flutter:
  uses-material-design: true

  assets:
  - assets/icons/
  - assets/icons/A/
  - assets/icons/A/Menu Bar/
  - assets/icons/B/
  - assets/icons/B/Menu Bar/
  - assets/icons/App Icon/
  - assets/icons/home/
  - assets/images/
  - assets/dialogs/

  fonts:
    - family: Helvetica
      fonts:
        - asset: assets/fonts/Helvetica.ttf
        - asset: assets/fonts/Helvetica-Bold.ttf
          weight: 700
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins.ttf
        - asset: assets/fonts/Poppins-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
